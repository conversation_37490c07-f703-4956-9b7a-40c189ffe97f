import { CaseSearchResults } from '@shared-types/types';
import { configureAppStore } from '@store/index';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '@store/modules/caseManager/slice';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { GQLApi } from '@utils/helpers';
import { Provider } from 'react-redux';
import { describe, expect, it, vi } from 'vitest';
import CaseTable, { Case } from '.';
import { render } from '../../../../test/render';
import { DataMap } from '@components/Table';
import { ConfigSliceState } from '@store/modules/config/slice';

const initialStateForMock: {
  caseManager: CaseManagerSliceState;
  appConfig: ConfigSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
  settings?: {
    fetchedStatuses: {
      status: string;
      error: string;
      statuses: { id: string; label: string; color: string; active: boolean }[];
      sdoId: string;
    };
    fetchedTags: {
      status: string;
      error: string;
      tags: { id: string; label: string }[];
      sdoId: string;
    };
  };
} = {
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
  caseManager: {
    ...caseManagerInitialState,
    folderContentTemplateSchema: {
      status: 'complete',
      error: '',
      id: 'folderContentTemplateSchemaId123',
    },
  },
  appConfig: {
    statusSchema: {
      status: 'complete',
      error: '',
      id: 'statusSchemaId123',
    },
    tagSchema: {
      status: 'complete',
      error: '',
      id: 'tagSchemaId123',
    },
    evidenceTypeSchema: {
      status: 'complete',
      error: '',
      id: '',
    },
  },
  settings: {
    fetchedStatuses: {
      status: 'complete',
      error: '',
      statuses: [
        { id: 'open', label: 'Case open', color: '#0000FF', active: true },
        { id: 'in_review', label: 'In Review', color: '#0000FF', active: true },
      ],
      sdoId: '',
    },
    fetchedTags: {
      status: 'complete',
      error: '',
      tags: [
        { id: 'tag_001', label: 'Tag 1' },
        { id: 'tag_002', label: 'Tag 2' },
        { id: 'tag_003', label: 'Tag 3' },
      ],
      sdoId: '',
    },
  },
};

const mockCaseSearchResults: CaseSearchResults = {
  searchMedia: {
    jsondata: {
      results: [
        {
          caseId: 'case_001',
          statusId: 'open',
          caseDate: '2025-03-20',
          caseName: 'mock casename 1',
          folderId: 'folder_001',
          createdBy: 'user_001',
          description: 'Investigation into reported theft at Warehouse A.',
          createdDateTime: '2025-03-20T09:00:00Z',
          modifiedDateTime: '2025-03-21T10:15:00Z',
          preconfiguredTagIds: ['tag_001', 'tag_003'],
          id: '1',
        },
        {
          caseId: 'case_002',
          statusId: 'in_review',
          caseDate: '2025-03-18',
          caseName: 'mock casename 2',
          folderId: 'folder_002',
          createdBy: 'user_002',
          description: 'Allegation of misconduct during work hours.',
          createdDateTime: '2025-03-18T14:20:00Z',
          modifiedDateTime: '2025-03-19T11:00:00Z',
          preconfiguredTagIds: ['tag_002'],
          id: '2',
        },
      ],
      totalResults: 2,
      limit: 10,
      from: 1,
      to: 2,
    },
  },
};

const mockCaseMap = mockCaseSearchResults.searchMedia.jsondata.results.reduce(
  (acc: DataMap<Case>, item, index) => {
    acc[item.id] = { index, item };
    return acc;
  },
  {}
);

const defaultProps = {
  caseMap: mockCaseMap,
  selected: '',
  setSelected: vi.fn(),
  handleSelect: vi.fn(),
  handleDoubleClick: vi.fn(),
  handleUploadFile: vi.fn(),
  pendingDeleteIds: [],
  setPendingDeleteIds: vi.fn(),
};

describe('Case Table Component', () => {
  vi.mock('@tanstack/react-virtual', () => ({
    useVirtualizer: vi.fn(() => ({
      getVirtualItems: () => [
        {
          index: 0,
          size: 69,
          start: 0,
          end: 69,
          key: 0,
          measureElement: vi.fn(),
        },
        {
          index: 1,
          size: 69,
          start: 69,
          end: 138,
          key: 1,
          measureElement: vi.fn(),
        },
      ],
      getTotalSize: () => 138,
      measure: vi.fn(),
      scrollToIndex: vi.fn(),
    })),
  }));

  it('should show loading state after filtering cases', async () => {
    const store = configureAppStore(initialStateForMock);

    const searchCases = vi
      .spyOn(GQLApi.prototype, 'searchCases')
      .mockImplementation(() => Promise.resolve(mockCaseSearchResults));

    vi.spyOn(GQLApi.prototype, 'getFolders').mockResolvedValue({
      data: { data: {} },
      errors: [],
    });

    render(
      <Provider store={store}>
        <CaseTable {...defaultProps} />
      </Provider>
    );

    expect(searchCases).toBeCalledTimes(1);

    const filterButton = screen.getByTestId('table-header__filter-list-icon');
    fireEvent.click(filterButton);
    await waitFor(() => {
      expect(screen.getByTestId('case-filter')).toBeInTheDocument();
    });

    const caseNameInput = screen.getByTestId('case-filter-caseName-textfield');
    fireEvent.change(caseNameInput, { target: { value: 'mock casename' } });

    const applyButton = screen.getByTestId('case-filter-apply-button');
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(searchCases).toBeCalledTimes(2);
    });

    await waitFor(() => {
      expect(screen.getByText('mock casename 1')).toBeInTheDocument();
      expect(screen.getByText('mock casename 2')).toBeInTheDocument();
    });
  });

  it('deletes case status on the case when Delete is selected', async () => {
    const store = configureAppStore(initialStateForMock);

    const searchCases = vi
      .spyOn(GQLApi.prototype, 'searchCases')
      .mockImplementation(() => Promise.resolve(mockCaseSearchResults));
    const updateCase = vi.spyOn(GQLApi.prototype, 'searchCases');

    render(
      <Provider store={store}>
        <CaseTable {...defaultProps} />
      </Provider>
    );

    expect(searchCases).toBeCalledTimes(1);

    const filterButton = screen.getByTestId('table-header__filter-list-icon');
    fireEvent.click(filterButton);
    await waitFor(() => {
      expect(screen.getByTestId('case-filter')).toBeInTheDocument();
    });

    const caseNameInput = screen.getByTestId('case-filter-caseName-textfield');
    fireEvent.change(caseNameInput, { target: { value: 'mock casename' } });

    const applyButton = screen.getByTestId('case-filter-apply-button');
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(searchCases).toBeCalledTimes(2);
    });

    await waitFor(() => {
      expect(screen.getByText('mock casename 1')).toBeInTheDocument();
      expect(screen.getByText('mock casename 2')).toBeInTheDocument();
    });

    const selectStatusButton = screen.queryAllByTestId('case-status-button');
    fireEvent.click(selectStatusButton[0]);

    await waitFor(() => {
      expect(screen.getByTestId('case-status-item-delete')).toBeInTheDocument();
    });

    const deleteStatusButton = screen.getByTestId('case-status-item-delete');
    fireEvent.click(deleteStatusButton);

    // The updateCase function is called with a statusIds of undefined which deletes the status
    await waitFor(() => {
      expect(updateCase).toHaveBeenCalledWith(
        expect.objectContaining({ statusIds: undefined })
      );
    });
  });
});
