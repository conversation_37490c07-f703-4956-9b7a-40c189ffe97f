import { describe, expect, it, vi } from 'vitest';
import { render } from '../../../test/render';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import CaseFilter from './index';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '../../store/modules/caseManager/slice.ts';
import { Provider } from 'react-redux';
import { configureAppStore } from '../../store';

const initialStateForMock: {
  caseManager: CaseManagerSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
  settings?: {
    fetchedStatuses: {
      status: string;
      error: string;
      statuses: { id: string; label: string; color: string; active: boolean }[];
      sdoId: string;
    };
    fetchedTags: {
      status: string;
      error: string;
      tags: { id: string; label: string }[];
      sdoId: string;
    };
    updatedStatuses: [];
  };
} = {
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
  caseManager: {
    ...caseManagerInitialState,
    folderContentTemplateSchema: {
      status: 'complete',
      error: '',
      id: 'folderContentTemplateSchemaId123',
    },
  },
  settings: {
    fetchedStatuses: {
      status: 'complete',
      error: '',
      statuses: [
        { id: 'open', label: 'Case open', color: '#0000FF', active: true },
        { id: 'in_review', label: 'In Review', color: '#0000FF', active: true },
      ],
      sdoId: '',
    },
    fetchedTags: {
      status: 'complete',
      error: '',
      tags: [
        { id: 'tag_001', label: 'Tag 1' },
        { id: 'tag_002', label: 'Tag 2' },
        { id: 'tag_003', label: 'Tag 3' },
      ],
      sdoId: '',
    },
    updatedStatuses: [],
  },
};

describe('CaseFilter Component', () => {
  const mockOnClose = vi.fn();
  const mockOnApply = vi.fn();

  const defaultProps = {
    filterValue: {
      caseName: '',
      caseId: '',
      statusId: '',
      tagIds: [],
    },
    onClose: mockOnClose,
    onApply: mockOnApply,
  };

  it('renders without crashing', () => {
    render(<CaseFilter {...defaultProps} />);
    expect(screen.getByTestId('case-filter')).toBeInTheDocument();

    const caseNameInput = screen.getByTestId('case-filter-caseName-textfield');
    expect(caseNameInput).toBeEmpty();

    const caseIdInput = screen.getByTestId('case-filter-caseId-textfield');
    expect(caseIdInput).toBeEmpty();

    const caseStatusSelect = screen.getByTestId('case-status-filter-select');
    expect(caseStatusSelect).toBeInTheDocument();

    const selectComboBoxes = screen.queryAllByRole('combobox');
    expect(selectComboBoxes).toHaveLength(2); // One for status, one for tags

    // Status has "Show All" option selected by default
    expect(selectComboBoxes[1].textContent).toEqual('Show All');
  });

  it('calls onClose when cancel button is clicked', () => {
    render(<CaseFilter {...defaultProps} />);
    screen.getByText('Cancel').click();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('calls onApply with correct values when apply button is clicked', async () => {
    const store = configureAppStore(initialStateForMock);

    render(
      <Provider store={store}>
        <CaseFilter {...defaultProps} />
      </Provider>
    );
    const applyButton = screen.getByTestId('case-filter-apply-button');

    expect(applyButton).toBeDisabled();

    // Add some text to the caseName input
    const caseNameInput = screen.getByTestId('case-filter-caseName-textfield');
    fireEvent.change(caseNameInput, { target: { value: 'mock casename' } });

    expect(applyButton).toBeEnabled();

    fireEvent.click(applyButton);
    await waitFor(() => {
      expect(mockOnApply).toHaveBeenCalledWith(
        {
          ...defaultProps.filterValue,
          caseName: 'mock casename',
        },
        expect.anything()
      );
    });
  });

  it('reverts to default values on clicking reset', async () => {
    const store = configureAppStore(initialStateForMock);

    render(
      <Provider store={store}>
        <CaseFilter {...defaultProps} />
      </Provider>
    );

    // Add some text to the caseName input
    const caseNameInput: HTMLInputElement = screen.getByTestId(
      'case-filter-caseName-textfield'
    );
    fireEvent.change(caseNameInput, { target: { value: 'mock casename' } });

    // Verify the input has changed
    expect(caseNameInput.value).toBe('mock casename');

    // Click the reset button
    const resetButton = screen.getByTestId('filter-reset-button');
    fireEvent.click(resetButton);

    // Verify the input has reverted to default
    await waitFor(() => {
      expect(caseNameInput.value).toBe('');
    });
  });

  it('validates the input caseName and caseId fields', async () => {
    const store = configureAppStore(initialStateForMock);

    render(
      <Provider store={store}>
        <CaseFilter {...defaultProps} />
      </Provider>
    );

    const applyButton = screen.getByTestId('case-filter-apply-button');

    expect(applyButton).toBeDisabled();

    // Add some text with invalid characters to the caseName input
    const caseNameInput: HTMLInputElement = screen.getByTestId(
      'case-filter-caseName-textfield'
    );
    fireEvent.change(caseNameInput, { target: { value: '$Hello%' } });

    // Verify the input has changed
    expect(caseNameInput.value).toBe('$Hello%');

    expect(applyButton).toBeEnabled();
    fireEvent.click(applyButton);

    // Verify the error message is displayed
    await waitFor(() => {
      expect(
        screen.getByText('Only letters, numbers, _ , and - allowed')
      ).toBeInTheDocument();
    });

    // Click the reset button
    const resetButton = screen.getByTestId('filter-reset-button');
    fireEvent.click(resetButton);

    // Verify the input has reverted to default
    await waitFor(() => {
      expect(caseNameInput.value).toBe('');
      expect(
        screen.queryByText('Only letters, numbers, _ , and - allowed')
      ).toBeNull();
    });

    const caseIdInput: HTMLInputElement = screen.getByTestId(
      'case-filter-caseId-textfield'
    );
    expect(caseIdInput).toBeEmpty();

    fireEvent.change(caseIdInput, { target: { value: '$CaseIdField Text%' } });

    // Verify the input has changed
    expect(caseIdInput.value).toBe('$CaseIdField Text%');

    expect(applyButton).toBeEnabled();
    fireEvent.click(applyButton);

    await waitFor(() => {
      expect(
        screen.getByText('Only letters, numbers, _ , and - allowed')
      ).toBeInTheDocument();
    });
  });
});
