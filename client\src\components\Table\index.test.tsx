import { Case } from '@components/CaseManager/CaseTable';
import Table, { Column, DataMap } from '@components/Table';
import { I18nTranslate } from '@i18n';
import { TableCell, TableRow } from '@mui/material';
import { VFile } from '@shared-types/types';
import { fireEvent, screen } from '@testing-library/react';
import { SortBy } from '@utils/helpers';
import { BrowserRouter } from 'react-router';
import { beforeAll, describe, expect, it, vi } from 'vitest';
import { render } from '../../../test/render';

const emptyState = (
  <TableRow>
    <TableCell>{I18nTranslate.TranslateMessage('noDataAvailable')}</TableCell>
  </TableRow>
);

const mockCases = [
  {
    caseId: '123',
    statusId: 'Open',
    caseDate: '2024-03-01',
    caseName: 'supper long name',
    folderId: 'folder-1',
    createdBy: 'User1',
    description: 'Description for Case A',
    createdDateTime: '2024-03-01T10:00:00Z',
    modifiedDateTime: '2024-03-02T12:00:00Z',
    preconfiguredTagIds: ['Tag1', 'Tag2'],
    id: 'case1',
  },
  {
    caseId: '456',
    statusId: 'Closed',
    caseDate: '2024-02-20',
    caseName: 'Case B',
    folderId: 'folder-2',
    createdBy: 'User2',
    description: 'Description for Case B',
    createdDateTime: '2024-02-20T08:30:00Z',
    modifiedDateTime: '2024-02-25T09:45:00Z',
    preconfiguredTagIds: ['Tag3'],
    id: 'case2',
  },
];

const mockCaseMap = mockCases.reduce((acc: DataMap<Case>, item, index) => {
  acc[item.id] = { index, item };
  return acc;
}, {});

const mockCaseColumns: Column<Case>[] = [
  {
    field: 'caseName',
    header: 'Case Name',
    isSortable: true,
  },
  {
    field: 'caseId',
    header: 'Case ID',
    isSortable: true,
  },
  {
    field: 'caseDate',
    header: 'Case Date',
    isSortable: true,
  },
  {
    field: 'modifiedDateTime',
    header: 'Retention Date',
    isSortable: true,
  },
  {
    field: 'statusId',
    header: 'Status',
  },
];

const defaultProps = {
  row: {
    selected: 'case1',
    handleSelect: vi.fn(),
    handleDoubleClick: vi.fn(),
    pendingDeleteIds: [],
  },
  sort: {
    handleSort: vi.fn(),
  },
  pagination: {
    rowsPerPage: 50,
    page: 1,
    onPageChange: vi.fn(),
    onRowsPerPageChange: vi.fn(),
    count: 100,
    rowsPerPageOptions: [50, 100, 150],
  },
  styles: {
    isLoading: false,
    className: 'class-name',
    emptyState,
  },
  extraProps: {},
  datatestId: 'table',
};

const mockFiles = [
  {
    id: 'file-001',
    fileName:
      'supper Document A supper Document A supper Document A supper Document A supper Document A supper Document A.pdf',
    fileType: 'image/jpeg',
    parentTreeObjectIds: ['folder-123', 'folder-456'],
    createdByName: 'User1',
    createdTime: '2024-03-01T10:00:00Z',
    updatedTime: '2024-03-02T12:00:00Z',
  },
  {
    id: 'file-002',
    fileName: 'Image B.png',
    fileType: 'text/csv',
    parentTreeObjectIds: ['folder-789'],
    createdByName: 'User2',
    createdTime: '2024-02-20T08:30:00Z',
    updatedTime: '2024-02-25T09:45:00Z',
  },
];

const mockFileMap = mockFiles.reduce((acc: DataMap<VFile>, item, index) => {
  acc[item.id] = { index, item };
  return acc;
}, {});

const mockFileColumns: Column<VFile>[] = [
  {
    field: 'fileType',
    header: 'Type',
  },
  {
    field: 'fileName',
    header: 'File Name',
  },
  {
    field: 'createdTime',
    header: 'Upload Date',
  },
  {
    field: 'id',
    header: 'Status',
  },
];

beforeAll(() => {
  window.HTMLElement.prototype.scrollIntoView = function () {};
});

describe('Table Component', () => {
  beforeAll(() => {
    window.HTMLElement.prototype.scrollIntoView = function () {};
  });

  vi.mock('@tanstack/react-virtual', () => ({
    useVirtualizer: vi.fn(() => ({
      getVirtualItems: () => [
        {
          index: 0,
          size: 69,
          start: 0,
          end: 69,
          key: 0,
          measureElement: vi.fn(),
        },
        {
          index: 1,
          size: 69,
          start: 69,
          end: 138,
          key: 1,
          measureElement: vi.fn(),
        },
      ],
      getTotalSize: () => 138,
      measure: vi.fn(),
      scrollToIndex: vi.fn(),
    })),
  }));

  it('renders table headers correctly', () => {
    render(
      <BrowserRouter>
        <Table
          columns={mockCaseColumns}
          data={mockCases}
          dataMap={mockCaseMap}
          {...defaultProps}
          sort={{
            ...defaultProps.sort,
            orderBy: SortBy.CaseDate,
            direction: 'asc',
          }}
        />
      </BrowserRouter>
    );
    mockCaseColumns.forEach(({ field }) => {
      expect(screen.getByTestId(`sort-label-${field}`)).toBeInTheDocument();
    });
  });

  it('renders table rows correctly', () => {
    render(
      <BrowserRouter>
        <Table
          columns={mockCaseColumns}
          data={mockCases}
          dataMap={mockCaseMap}
          {...defaultProps}
          sort={{
            ...defaultProps.sort,
            orderBy: SortBy.CaseDate,
            direction: 'asc',
          }}
        />
      </BrowserRouter>
    );
    mockCases.forEach((row) => {
      expect(screen.getByText(row.caseName)).toBeInTheDocument();
      expect(screen.getByText(row.caseId)).toBeInTheDocument();
      const caseDates = screen.getAllByText(row.caseDate);
      expect(caseDates.length).toBeGreaterThan(0);
      const retentionDates = screen.getAllByText(row.modifiedDateTime);
      expect(retentionDates.length).toBeGreaterThan(0);
      expect(screen.getAllByText(row.statusId).length).toBeGreaterThan(0);
    });
  });

  it('calls handleSelect when a row is clicked', () => {
    render(
      <BrowserRouter>
        <Table
          columns={mockCaseColumns}
          data={mockCases}
          dataMap={mockCaseMap}
          {...defaultProps}
          sort={{
            ...defaultProps.sort,
            orderBy: SortBy.CaseDate,
            direction: 'asc',
          }}
        />
      </BrowserRouter>
    );
    const firstRow = screen.getByText(mockCases[0].caseName);
    fireEvent.click(firstRow);
    expect(defaultProps.row.handleSelect).toHaveBeenCalledWith(mockCases[0].id);
  });

  it('calls handleSort when a header is clicked', () => {
    render(
      <BrowserRouter>
        <Table
          columns={mockCaseColumns}
          data={mockCases}
          dataMap={mockCaseMap}
          {...defaultProps}
          sort={{
            ...defaultProps.sort,
            orderBy: SortBy.CaseDate,
            direction: 'asc',
          }}
        />
      </BrowserRouter>
    );
    const firstHeader = screen.getByTestId(`sort-${mockCaseColumns[0].field}`);
    fireEvent.click(firstHeader);
    expect(defaultProps.sort.handleSort).toHaveBeenCalledWith(
      mockCaseColumns[0].field
    );
  });

  it('calls handleDoubleClick when a row is double-clicked', () => {
    render(
      <BrowserRouter>
        <Table
          columns={mockFileColumns}
          data={mockFiles}
          dataMap={mockFileMap}
          {...defaultProps}
          sort={{
            ...defaultProps.sort,
            orderBy: 'fileType',
            direction: 'asc',
          }}
        />
      </BrowserRouter>
    );

    const firstRow = screen.getByText(mockFiles[0].fileName);
    fireEvent.doubleClick(firstRow);
    expect(defaultProps.row.handleDoubleClick).toHaveBeenCalled();
  });

  it('displays empty state when no data', () => {
    render(
      <BrowserRouter>
        <Table
          columns={mockFileColumns}
          data={[]}
          dataMap={{}}
          {...defaultProps}
          sort={{
            ...defaultProps.sort,
            orderBy: 'fileType',
            direction: 'asc',
          }}
          styles={{
            ...defaultProps.styles,
            emptyState: emptyState,
          }}
        />
      </BrowserRouter>
    );

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('selects first row when Tab is pressed and no row is selected', () => {
    render(
      <BrowserRouter>
        <Table
          columns={mockCaseColumns}
          data={mockCases}
          dataMap={mockCaseMap}
          {...defaultProps}
          row={{
            ...defaultProps.row,
            selected: '',
          }}
          sort={{
            ...defaultProps.sort,
            orderBy: SortBy.CaseDate,
            direction: 'asc',
          }}
        />
      </BrowserRouter>
    );
    const table = screen.getByTestId('table');
    fireEvent.keyDown(table, { key: 'Tab' });
    expect(defaultProps.row.handleSelect).toHaveBeenCalledWith(mockCases[0].id);
  });

  it('should select next row when pressing "ArrowRight" or "ArrowDown"', () => {
    render(
      <Table
        columns={mockCaseColumns}
        data={mockCases}
        dataMap={mockCaseMap}
        {...defaultProps}
        sort={{
          ...defaultProps.sort,
          orderBy: SortBy.CaseDate,
          direction: 'asc',
        }}
      />
    );

    // select next case with ArrowRight
    const table = screen.getByTestId('table');
    fireEvent.keyDown(table, { key: 'ArrowRight' });
    expect(defaultProps.row.handleSelect).toHaveBeenCalledWith('case2');

    // select next case with ArrowDown
    fireEvent.keyDown(table, { key: 'ArrowDown' });
    expect(defaultProps.row.handleSelect).toHaveBeenCalledWith('case2');
  });

  it("should stay at the last row when pressing on 'ArrowRight' or 'ArrowDown' at the last row", () => {
    render(
      <Table
        columns={mockCaseColumns}
        data={mockCases}
        dataMap={mockCaseMap}
        {...defaultProps}
        sort={{
          ...defaultProps.sort,
          orderBy: SortBy.CaseDate,
          direction: 'asc',
        }}
        extraProps={{}}
        row={{
          ...defaultProps.row,
          selected: 'case2', // select last case initially
        }}
      />
    );

    const table = screen.getByTestId('table');
    fireEvent.keyDown(table, { key: 'ArrowRight' });
    expect(defaultProps.row.handleSelect).not.toHaveBeenCalled();
  });

  it('should select prev row when pressing "ArrowLeft" or "ArrowUp"', () => {
    render(
      <Table
        columns={mockCaseColumns}
        data={mockCases}
        dataMap={mockCaseMap}
        {...defaultProps}
        sort={{
          ...defaultProps.sort,
          orderBy: SortBy.CaseDate,
          direction: 'asc',
        }}
        extraProps={{}}
        row={{
          ...defaultProps.row,
          selected: 'case2', // select last case initially
        }}
      />
    );

    // select prev case with ArrowLeft
    const table = screen.getByTestId('table');
    fireEvent.keyDown(table, { key: 'ArrowLeft' });
    expect(defaultProps.row.handleSelect).toHaveBeenCalledWith('case1');

    // select prev case with ArrowUp
    fireEvent.keyDown(table, { key: 'ArrowUp' });
    expect(defaultProps.row.handleSelect).toHaveBeenCalledWith('case1');
  });

  it("should stay at the first row when pressing on 'ArrowUp' or 'ArrowLeft' at the first row", () => {
    render(
      <Table
        columns={mockCaseColumns}
        data={mockCases}
        dataMap={mockCaseMap}
        {...defaultProps}
        sort={{
          ...defaultProps.sort,
          orderBy: SortBy.CaseDate,
          direction: 'asc',
        }}
      />
    );

    const table = screen.getByTestId('table');
    fireEvent.keyDown(table, { key: 'ArrowLeft' });
    expect(defaultProps.row.handleSelect).not.toHaveBeenCalled();
  });
});
