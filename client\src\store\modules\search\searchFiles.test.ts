import {
  filenameMockResponse,
  objectSearch,
  objectSearchAutocompleteResult,
  textRecognitionMockResponse,
  transcriptionMockResponse,
  optimizeQueryMock,
  optimizeQueryMockResult,
} from '@store/modules/search/fixtures';
import { waitFor } from '@testing-library/dom';
import { GQLApi } from '@utils/helpers';
import axios from 'axios';
import { afterEach } from 'node:test';
import { describe, expect, it, vi } from 'vitest';
import config from '../../../../config.json';
import { searchFiles, optimizeQuery } from './searchFiles';
import { FileSearchParams } from './slice';

const searchFilesParams: FileSearchParams = {
  keywordSearchQuery: 'test',
  searchResultType: 'grouped',
  checkedResultCategories: ['transcription', 'textRecognition'],
  sort: {
    type: 'createdTime',
    order: 'desc',
  },
  uploadDateFilter: {
    startDate: '',
    endDate: '',
  },
  caseIdFilter: [''],
  caseStatusIdsFilter: [''],
  cognitionFilters: {
    wordsInTranscript: '',
    textRecognition: '',
  },
  evidenceTypeFilter: [],
  fileTypeFilter: [],
  pagination: {
    ungrouped: {
      offset: 0,
      limit: 50,
    },
    transcription: {
      offset: 0,
      limit: 10,
    },
    faceRecognition: {
      offset: 0,
      limit: 10,
    },
    objectDetection: {
      offset: 0,
      limit: 10,
    },
    vehicleRecognition: {
      offset: 0,
      limit: 10,
    },
    licensePlateRecognition: {
      offset: 0,
      limit: 10,
    },
    sceneClassification: {
      offset: 0,
      limit: 10,
    },
    textRecognition: {
      offset: 0,
      limit: 10,
    },
    metadata: {
      offset: 0,
      limit: 10,
    },
  },
};

const searchFilesParamsFilename: FileSearchParams = {
  keywordSearchQuery: 'test',
  searchResultType: 'grouped',
  checkedResultCategories: ['filename'],
  sort: {
    type: 'createdTime',
    order: 'desc',
  },
  uploadDateFilter: {
    startDate: '',
    endDate: '',
  },
  caseIdFilter: [''],
  caseStatusIdsFilter: [''],
  cognitionFilters: {
    filename: 'test file name',
  },
  evidenceTypeFilter: [],
  fileTypeFilter: [],
  pagination: {
    ungrouped: {
      offset: 0,
      limit: 50,
    },
    transcription: {
      offset: 0,
      limit: 10,
    },
    faceRecognition: {
      offset: 0,
      limit: 10,
    },
    objectDetection: {
      offset: 0,
      limit: 10,
    },
    vehicleRecognition: {
      offset: 0,
      limit: 10,
    },
    licensePlateRecognition: {
      offset: 0,
      limit: 10,
    },
    sceneClassification: {
      offset: 0,
      limit: 10,
    },
    textRecognition: {
      offset: 0,
      limit: 10,
    },
    metadata: {
      offset: 0,
      limit: 10,
    },
  },
};

const searchFilesParamsObjectSearch: FileSearchParams = {
  keywordSearchQuery: 'test',
  searchResultType: 'ungrouped',
  checkedResultCategories: ['objectDetection'],
  sort: {
    type: 'dateUploaded',
    order: 'desc',
  },
  uploadDateFilter: {
    startDate: '',
    endDate: '',
  },
  caseIdFilter: [''],
  caseStatusIdsFilter: [''],
  evidenceTypeFilter: [],
  fileTypeFilter: [],
  pagination: {
    ungrouped: {
      offset: 0,
      limit: 50,
    },
  },
};

const searchFilesParamsUpdated: FileSearchParams = {
  ...searchFilesParams,
  pagination: {
    ...searchFilesParams.pagination,
    transcription: {
      ...searchFilesParams.pagination.transcription,
      offset: 10,
      limit: 100,
      isUpdate: true,
    },
  },
};

vi.mock('../../../utils/helpers/gqlApi/baseGraphQLApi', () => ({
  baseGraphQLApiThrowError: vi.fn(),
  baseGraphQLApi: vi.fn(),
}));

describe('caseDetailSlice', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should get files by name and sort by create time', async () => {
    const searchFilesSpy = vi
      .spyOn(GQLApi.prototype, 'searchFiles')
      .mockImplementation(({ queryName }) => {
        if (queryName === 'textRecognitionFileSearch') {
          return Promise.resolve(textRecognitionMockResponse);
        }
        if (queryName === 'transcriptionFileSearch') {
          return Promise.resolve(transcriptionMockResponse);
        }
        return Promise.resolve(textRecognitionMockResponse);
      });

    vi.spyOn(GQLApi.prototype, 'getSDOSchemaId').mockImplementation(() =>
      Promise.resolve('e9b53bff-fadb-4e9d-96d4-b49890e4a058')
    );

    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = '4cfe23f1-094e-42bf-8b39-37c5e9d06e01';
    const gql = new GQLApi(endpoint, token, veritoneAppId);

    searchFiles({
      params: searchFilesParams,
      gql,
      config,
      dispatch: vi.fn(),
      token: `bearer ${token}`,
    });

    await waitFor(() => {
      expect(searchFilesSpy).toHaveBeenCalledTimes(2);
    });

    searchFiles({
      params: searchFilesParamsUpdated,
      gql,
      config,
      dispatch: vi.fn(),
      lastParams: searchFilesParams,
      token: `bearer ${token}`,
    });

    // since the search files was called with a single update to pagination, it should only be called one more time
    await waitFor(() => {
      expect(searchFilesSpy).toHaveBeenCalledTimes(3);
    });
  });

  it('should search files by name', async () => {
    const searchFilesSpy = vi
      .spyOn(GQLApi.prototype, 'searchFiles')
      .mockImplementation(({ queryName }) => {
        if (queryName === 'filenameFileSearch') {
          return Promise.resolve(filenameMockResponse);
        }
        return Promise.resolve(textRecognitionMockResponse);
      });

    vi.spyOn(GQLApi.prototype, 'getSDOSchemaId').mockImplementation(() =>
      Promise.resolve('e9b53bff-fadb-4e9d-96d4-b49890e4a058')
    );

    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = '4cfe23f1-094e-42bf-8b39-37c5e9d06e01';
    const gql = new GQLApi(endpoint, token, veritoneAppId);

    searchFiles({
      params: searchFilesParamsFilename,
      gql,
      config,
      dispatch: vi.fn(),
      token: `bearer ${token}`,
    });

    await waitFor(() => {
      expect(searchFilesSpy).toHaveBeenCalledTimes(1);
    });

    expect(searchFilesSpy).toHaveBeenCalledWith({
      searchVars: {
        search: {
          offset: 0,
          limit: 10,
          index: ['mine'],
          type: 'file',
          query: {
            operator: 'and',
            conditions: [
              {
                operator: 'query_string',
                field: 'veritone-file.filename',
                value: '*test*',
              },
              {
                operator: 'query_string',
                field: 'veritone-file.filename',
                value: '*test file name*',
              },
            ],
          },
          select: ['veritone-file'],
          sort: [
            {
              field: 'createdTime',
              order: 'desc',
            },
          ],
        },
      },
      queryName: 'filenameFileSearch',
    });
  });

  it('can create object search queries in an ungrouped search with a keyword', async () => {
    const searchFilesSpy = vi
      .spyOn(GQLApi.prototype, 'searchFiles')
      .mockImplementation(({ queryName }) => {
        if (queryName === 'filenameFileSearch') {
          return Promise.resolve(filenameMockResponse);
        }
        return Promise.resolve(textRecognitionMockResponse);
      });

    const axiosSpy = vi.spyOn(axios, 'post').mockImplementation((url) => {
      console.log('axios post url', url);
      if (url.includes('autocomplete')) {
        return Promise.resolve(objectSearchAutocompleteResult);
      }
      return Promise.resolve({
        data: {},
      });
    });

    vi.spyOn(GQLApi.prototype, 'getSDOSchemaId').mockImplementation(() =>
      Promise.resolve('e9b53bff-fadb-4e9d-96d4-b49890e4a058')
    );

    const endpoint = `${config.apiRoot}/v3/graphql`;
    const veritoneAppId = config.veritoneAppId;
    const token = '4cfe23f1-094e-42bf-8b39-37c5e9d06e01';
    const gql = new GQLApi(endpoint, token, veritoneAppId);
    const dispatch = vi.fn();

    searchFiles({
      params: searchFilesParamsObjectSearch,
      gql,
      config,
      dispatch,
      token: `bearer ${token}`,
    });

    await waitFor(() => {
      expect(searchFilesSpy).toHaveBeenCalledTimes(1);
      expect(axiosSpy).toHaveBeenCalledTimes(1);
    });

    const result = optimizeQuery(objectSearch);

    await waitFor(() => {
      expect(searchFilesSpy).toBeCalledWith({
        searchVars: result,
        queryName: 'ungroupedFileSearch',
        abortSignal: undefined,
      });
    });
  });

  it('can optimize queries with optimizeQuery', () => {
    const result = optimizeQuery(optimizeQueryMock);

    expect(result.search.query).toEqual(optimizeQueryMockResult.search.query);
  });
});
