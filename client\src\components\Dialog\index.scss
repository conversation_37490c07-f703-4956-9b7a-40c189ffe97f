.dialog {
  .Sdk-MuiDialog-paper {
    min-width: 400px;
    padding: 30px;
    min-width: 400px;
    border-radius: 10px;
  }

  .dialog-title {
    gap: 10px;
    display: flex;
    font-size: 18px;
    font-weight: bold;
    align-items: center;
    color: var(--text-primary);
    justify-content: space-between;

    .icon-button {
      &.Sdk-MuiButtonBase-root {
        width: 24px;
        height: 24px;
        min-width: 0;
        border-radius: 90px;
        color: var(--text-primary);
      }
    }
  }

  .dialog-content {
    margin-top: 20px;
    margin-bottom: 35px;
    font-size: 14px;
    color: var(--text-primary);
  }

  .dialog-content__type-confirmation {
    span {
      color: var(--button-destructive-action);
    }

    .Sdk-MuiFormControl-root {
      margin: 10px 0;
      width: 100%;
    }

  }

  .dialog-buttons {
    gap: 10px;
    display: flex;
    justify-content: flex-end;

    .dialog-buttons__close-button {
      padding: 10px 25px;
      color: var(--text-primary);
    }

    .dialog-buttons__confirm-button {
      padding: 10px 25px;
      text-transform: unset;
    }

    .dialog-buttons__confirm-button:not(.disabled, .delete) {
      padding: 10px 25px;
      color: white;
      background: var(--button-dark-blue);
    }

    .delete {
      color: white;
      background: var(--button-destructive-action);
    }
  }

  &.use-type-confirmation {
    .Sdk-MuiDialog-paper {
      padding: 30px;
      width: 518px;
      border-radius: 10px;
    }

    .dialog-content {
      :nth-child(2) {
        margin-top: 28px;
        padding-left: 10px;
      }

      :nth-child(3) {
        margin-top: 15px;
        padding-left: 10px;
      }

      span {
        display: inline-flex;
        justify-content: center;
        align-items: center;

        svg {
          height: 18px;
          margin-right: 12px;
        }
      }
    }

    .dialog-content__type-confirmation {
      margin-bottom: 42px;

      span {
        color: var(--button-destructive-action);
      }

      .Sdk-MuiFormControl-root {
        margin-top: 10px;
        width: 100%;
      }
    }
  }
}