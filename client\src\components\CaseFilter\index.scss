.case-filter {
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;

  .case-filter__header-container {
    padding: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: var(--background-primary);

    .case-filter__header-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .case-filter__header-actions {
      gap: 10px;
      display: flex;
      align-items: center;
    }

    .case-filter__apply-button {
      color: var(--button-dark-blue);
    }
  }

  .case-filter__main-container {
    flex: 1;
    padding: 20px;
    background-color: var(--background-primary);
    font-size: 12px;
    line-height: 30px;

    .case-filter__caseName-field {
      margin-bottom: 20px;
    }

    .case-filter__caseId-field {
      margin-bottom: 20px;
    }
    .case-filter__tags-form-field {
      min-width: 300px;
      margin-bottom: 20px;
    }

    .case-filter__status-form-field {
      min-width: 150px;
      margin-bottom: 20px;
    }
  }

  .case-filter__footer-container {
    gap: 10px;
    padding: 30px;
    display: flex;
    justify-content: flex-end;
    background-color: var(--background-primary);
  }
}
