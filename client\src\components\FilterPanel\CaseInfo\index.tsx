import './index.scss';
import MultiSelect from '@components/MultiSelect';
import { useValidTags } from '@hooks';
import { I18nTranslate } from '@i18n';
import { MenuItem, TextField } from '@mui/material';
import { CaseTag, FileStatus, FilterFormValue } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  selectStatusSchema,
  selectTagSchema,
} from '@store/modules/config/slice';
import {
  fetchStatuses,
  fetchTags,
  selectFetchedStatuses,
} from '@store/modules/settings/slice';
import { useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { useSelector } from 'react-redux';
import FilterDate from './FilterDate';
import FilterSelect from './FilterSelect';

// const mockFileStatus = ['Pending', 'Completed', 'In Progress'];
const fileStatuses: FileStatus[] = [
  { id: '1', label: 'Pending' },
  { id: '2', label: 'Completed' },
  { id: '3', label: 'In Progress' },
];

interface Props {
  isCaseDetail?: boolean;
}

const CaseInformation = ({ isCaseDetail }: Props) => {
  const intl = I18nTranslate.Intl();
  const { control, watch } = useFormContext<FilterFormValue>();
  const dispatch = useAppDispatch();

  const statusSchemaId = useSelector(selectStatusSchema).id;
  const tagSchemaId = useSelector(selectTagSchema).id;
  const statuses = useSelector(selectFetchedStatuses);
  const validStatuses = statuses.filter((status) => status.active);

  const caseTagIds = watch('filterCaseTagIds') || [];
  const { tags } = useValidTags(caseTagIds);

  useEffect(() => {
    if (statusSchemaId) {
      dispatch(fetchStatuses({}));
    }
  }, [statusSchemaId]);

  useEffect(() => {
    if (tagSchemaId) {
      dispatch(fetchTags({}));
    }
  }, [tagSchemaId]);

  return (
    <div className="case-info" data-testid="filter-case-information">
      <div className="case-info__item">
        <span>{intl.formatMessage({ id: 'caseStatus' })}</span>
        <FilterSelect
          width={155}
          name="caseStatus"
          isCaseDetail={isCaseDetail}
          statuses={validStatuses}
        >
          {validStatuses.map((status) => (
            <MenuItem
              key={status.id}
              value={status.id}
              data-testid={`caseStatus-${status.id}`}
            >
              {status.label}
            </MenuItem>
          ))}
        </FilterSelect>
      </div>
      <div className="case-info__item">
        <span>
          {intl.formatMessage({ id: 'caseTags', defaultMessage: 'Case tags' })}
        </span>
        <MultiSelect<CaseTag>
          name="filterCaseTagIds"
          options={tags}
          currentIds={caseTagIds}
          isFullWidth
        />
      </div>
      <div className="case-info__item">
        <span>{intl.formatMessage({ id: 'caseID' })}</span>
        <Controller
          control={control}
          name="caseId"
          render={({ field }) => (
            <TextField
              {...field}
              className="case-info__item-caseId"
              disabled={isCaseDetail}
              data-testid="case-info__item-caseId"
            />
          )}
        />
      </div>
      <div className="case-info__item">
        <span>{intl.formatMessage({ id: 'uploadDate' })}</span>
        <FilterDate name="uploadDate" />
      </div>
      {/* Leave this commented section here.  Retention Date will be part of future phase */}
      {/* <div className="case-info__item">*/}
      {/*   <span>{intl.formatMessage({ id: 'retentionDate' })}</span>*/}
      {/*   <FilterDate name="retentionDate" />*/}
      {/* </div>*/}
      <div className="case-info__item">
        <span>{intl.formatMessage({ id: 'fileStatus' })}</span>
        <FilterSelect width={141} name="fileStatus" statuses={fileStatuses}>
          {fileStatuses.map((status) => (
            <MenuItem
              key={status.id}
              value={status.id}
              data-testid={`fileStatus-${status.id}`}
            >
              {status.label}
            </MenuItem>
          ))}
        </FilterSelect>
      </div>
    </div>
  );
};

export default CaseInformation;
