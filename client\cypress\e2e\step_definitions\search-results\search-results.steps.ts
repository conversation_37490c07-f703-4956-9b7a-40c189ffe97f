import {
  Before,
  DataTable,
  Given,
  Then,
  When,
} from '@badeball/cypress-cucumber-preprocessor';
import { searchPage } from '../../../pages/searchPage';
import { Graphql } from '../../../support/helperFunction/caseManagerHelper';
import {
  CognitionInput,
  CognitionPlaceholder,
  DropdownBoxCaseType,
  EvidenceTypeInput,
  FileTypeInput,
  GroupView,
  ViewType,
} from '../../../support/helperFunction/searchResultsHelper';
import '../common/common.step';

Before(() => {
  cy.LoginToApp();
  cy.interceptGraphQLQuery(Graphql.FetchCaseStatuses, 'fetchCaseStatuses');
  cy.interceptGraphQLQuery(Graphql.FetchTags, 'fetchTags');
  cy.intercept('POST', /v3\/graphql/, (req) => {
    if (req.body.query && req.body.query.includes('searchMedia')) {
      req.alias = 'searchMedia';
    }
  });
});

Given('The user is on the Search screen', () => {
  searchPage.visit();
  cy.awaitNetworkResponseCode({ alias: '@fetchCaseStatuses', code: 200 });
  cy.awaitNetworkResponseCode({ alias: '@fetchTags', code: 200 });
  searchPage.goToSearchPage();
});

When(
  'The user verifies that the information on the search page is correct',
  () => {
    searchPage.verifySearchPage();
  }
);

When('The user enters keyword {string}', (keyword: string) => {
  searchPage.enterSearchKeyword(keyword);
});

Then(
  'The user sees case name {string} in search results',
  (caseName: string) => {
    searchPage.verifySearchResults(caseName);
  }
);

When(
  'The user picks {string} from the {string} dropdown box in case information section',
  (caseInformation: string, dropdownBox: DropdownBoxCaseType) => {
    searchPage.pickCaseInformation(caseInformation, dropdownBox);
  }
);

When('The user chooses the first case in the list', () => {
  searchPage.chooseFirstCaseInList();
});

Then('The user clicks on kebab icon of first row in search results', () => {
  searchPage.clickKebabIconFirstRow();
});

Then('The user clicks on {string} in kebab menu', (menuItemName: string) => {
  searchPage.selectKebabMenuItem(menuItemName);
});

Then('The user inputs case ID {string}', (caseId: string) => {
  searchPage.InputCaseId(caseId);
});

Then(
  'The user selects a date range from day {int} to day {int} in {string}',
  (startDay: number, endDay: number, month: string) => {
    searchPage.PickDateRange(startDay, endDay, month);
  }
);

When(
  /^The user (?:opens|closes) the "([^"]*)" section$/,
  (caseTypeName: string) => {
    searchPage.ClickToOpenSearchInCaseType(caseTypeName);
  }
);

Then(
  'The user inputs {string} into the text field with placeholder {string} in the advanced search section',
  (input: string, placeholderText: CognitionPlaceholder) => {
    const inputs: CognitionInput[] = [
      { placeholder: placeholderText, value: input },
    ];
    searchPage.InputCognitionFields(inputs);
  }
);

Then(
  'The user inputs the following values into the advanced search fields:',
  (dataTable: DataTable) => {
    const inputs: CognitionInput[] = dataTable.hashes().map((row) => ({
      placeholder: row.placeholder as CognitionPlaceholder,
      value: row.value,
    }));
    searchPage.InputCognitionFields(inputs);
  }
);

Then('The user checks evidence type {string}', (evidenceType: string) => {
  const typesToCheck: EvidenceTypeInput[] = [{ evidenceType }];
  searchPage.CheckEvidenceTypes(typesToCheck);
});

Then(
  'The user checks the following evidence types:',
  (dataTable: DataTable) => {
    const evidenceTypes: EvidenceTypeInput[] = dataTable
      .hashes()
      .map((row) => ({ evidenceType: row.evidenceType }));
    searchPage.CheckEvidenceTypes(evidenceTypes);
  }
);

Then('The user checks file type {string}', (fileType: string) => {
  const typesToCheck: FileTypeInput[] = [{ fileType }];
  searchPage.CheckFileTypes(typesToCheck);
});

Then('The user checks the following file types:', (dataTable: DataTable) => {
  const fileTypes: FileTypeInput[] = dataTable
    .hashes()
    .map((row) => ({ fileType: row.fileType }));
  searchPage.CheckFileTypes(fileTypes);
});

Then('The user clicks on "Reset All" button', () => {
  searchPage.ClickResetAllButton();
});

Then(
  'The user verifies that the search box and all search fields are reset',
  () => {
    searchPage.VerifyResetAll();
  }
);

When('The user changes group view to {string}', (view: GroupView) => {
  searchPage.ChangeGroupView(view);
});

Then(
  'The user verifies that the text {string} is visible in the file detail view',
  (expectedText: string) => {
    searchPage.verifyTextInFileDetailView(expectedText);
  }
);

When('The user selects {string} rows per page', (rowsPerPage: string) => {
  searchPage.selectRowsPerPage(rowsPerPage);
});

Then(
  'The total number of rows displayed should be at most {int}',
  (maxRows: number) => {
    searchPage.verifyMaxRowsDisplayed(maxRows);
  }
);

When('The user clicks the next page button', () => {
  searchPage.clickNextPageButton();
});

When('The user clicks the previous page button', () => {
  searchPage.clickPreviousPageButton();
});

Then('The user should see page range {string}', (expectedRange: string) => {
  searchPage.verifyPageRange(expectedRange);
});

When('The user verifies that view toggle buttons are present', () => {
  searchPage.verifyViewToggleButtonsPresent();
});

When(
  'The user verifies the current view type is {string}',
  (viewType: ViewType) => {
    searchPage.verifyCurrentViewType(viewType);
  }
);

When('The user clicks the view toggle button', () => {
  searchPage.clickViewToggleButton();
});

Then('The user should see the list view is active', () => {
  searchPage.verifyListViewActive();
});

Then('The user should see the grid view is active', () => {
  searchPage.verifyGridViewActive();
});

Then('The user clicks the search button', () => {
  searchPage.clickSearchButton();
});

Then(
  'The user verifies that the search page shows the init empty state',
  () => {
    searchPage.verifyInitEmptyState();
  }
);

Then('The user sees search results', () => {
  searchPage.validateSearchResultsState();
});

Then('The error message {string} should be shown', (errorMessage: string) => {
  searchPage.verifyErrorMessage(errorMessage);
});
