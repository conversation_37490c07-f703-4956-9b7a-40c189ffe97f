import { Then, When } from '@badeball/cypress-cucumber-preprocessor';

When(
  /^The user presses "([^"]+)" key (\d+) time\(s\)$/,
  (key: string, count: number) => {
    for (let i = 0; i < count; i++) {
      cy.focused().trigger('keydown', { key: key });
    }
  }
);

When('The user presses {string} key on the table', (key: string) => {
  cy.pressKeyOnTable(key);
});

Then('The {string} item in the table is selected', (position: string) => {
  cy.verifyTableRowSelected(position);
});
