import EmptyState from '@pages/Settings/EmptyState';
import { StatusTagRow, Visibility } from '@store/modules/settings/slice';
import { screen, waitFor } from '@testing-library/react';
import { uniqueId } from 'lodash';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ReorderTable from '.';
import { render } from '../../../test/render';

const mockData: StatusTagRow[] = [
  {
    id: uniqueId(),
    name: 'Open',
    color: '#FF0000',
    visibility: Visibility.Active,
  },
  {
    id: uniqueId(),
    name: 'Closed',
    color: '#0000FF',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Pending Trial',
    color: '#00FF00',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Open 2',
    color: '#FF0000',
    visibility: Visibility.Active,
  },
  {
    id: uniqueId(),
    name: 'Closed 2',
    color: '#0000FF',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Pending Trial 2',
    color: '#00FF00',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Open 3',
    color: '#FF0000',
    visibility: Visibility.Active,
  },
  {
    id: uniqueId(),
    name: 'Closed 3',
    color: '#0000FF',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Pending Trial 3',
    color: '#00FF00',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Open 4',
    color: '#FF0000',
    visibility: Visibility.Active,
  },
  {
    id: uniqueId(),
    name: 'Closed 4',
    color: '#0000FF',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Pending Trial 4',
    color: '#00FF00',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Open 5',
    color: '#FF0000',
    visibility: Visibility.Active,
  },
  {
    id: uniqueId(),
    name: 'Closed 5',
    color: '#0000FF',
    visibility: Visibility.Inactive,
  },
  {
    id: uniqueId(),
    name: 'Pending Trial 5',
    color: '#00FF00',
    visibility: Visibility.Inactive,
  },
];

const defaultProps = {
  data: mockData,
  deleteRow: null,
  setDeleteRow: vi.fn(),
  isLoading: false,
  emptyState: (
    <EmptyState
      option={'statusLabels'}
      onClick={function (): void {
        throw new Error('Function not implemented.');
      }}
    />
  ),
  validateName: vi.fn(),
  setUnsavedChanges: vi.fn(),
};

describe('Create Case', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the ReorderTable', async () => {
    render(<ReorderTable {...defaultProps} />);

    await waitFor(() => {
      expect(screen.getByTestId('reorder-table')).toBeInTheDocument();
    });
  });

  it('renders the ReorderTable in edit mode', async () => {
    render(<ReorderTable {...defaultProps} editMode colorRow />);

    // Table exists
    await waitFor(() => {
      expect(screen.getByTestId('reorder-table')).toBeInTheDocument();
    });

    // All Edit Mode drag handles exist
    await waitFor(() => {
      expect(screen.queryAllByTestId('reorder-table-drag-handle').length).toBe(
        mockData.length
      );
    });

    // All Edit Mode name text fields exist
    await waitFor(() => {
      expect(screen.queryAllByTestId('reorder-table-name-edit').length).toBe(
        mockData.length
      );
    });

    // All Edit Mode color pickers exist
    await waitFor(() => {
      expect(screen.queryAllByTestId('color-picker').length).toBe(
        mockData.length
      );
    });

    // All Edit Mode visibility pickers exist
    await waitFor(() => {
      expect(
        screen.queryAllByTestId('reorder-table-visibility-edit').length
      ).toBe(mockData.length);
    });
  });
});
