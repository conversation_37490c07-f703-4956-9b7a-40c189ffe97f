import { configureAppStore } from '@store/index';
import {
  CaseDetailSliceState,
  initialState as caseDetailInitialState,
} from '@store/modules/caseDetail/slice';
import { toggleOpenEditDrawer } from '@store/modules/metadata/slice';
import { fireEvent, screen, waitFor, within } from '@testing-library/dom';
import { act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from '@theme';
import { GQLApi } from '@utils/helpers';
import {
  dispatchCustomEvent,
  eventHistory,
} from '@utils/helpers/dispatchCustomEvent';
import { ViewType } from '@utils/local-storage/viewTypes';
import { clone } from 'lodash';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router';
import { describe, expect, it, vi } from 'vitest';
import FileTable from '.';
import { render } from '../../../../test/render';

const mockFiles = [
  {
    id: 'file_1',
    fileName: 'file1',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user1',
    createdTime: '2021-09-01T00:00:00Z',
    updatedTime: '2021-09-01T00:00:00Z',
    description: '',
  },
  {
    id: 'file_2',
    fileName: 'file2',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user2',
    createdTime: '2021-09-02T00:00:00Z',
    updatedTime: '2021-09-02T00:00:00Z',
    description: '',
  },
  {
    id: 'file_3',
    fileName: 'file3',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user3',
    createdTime: '2021-09-03T00:00:00Z',
    updatedTime: '2021-09-03T00:00:00Z',
    description: '',
  },
  {
    id: 'file_4',
    fileName: 'file4',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user4',
    createdTime: '2021-09-01T00:00:00Z',
    updatedTime: '2021-09-01T00:00:00Z',
    description: '',
  },
  {
    id: 'file_5',
    fileName: 'file5',
    fileType: 'image',
    parentTreeObjectIds: ['folder_1'],
    createdByName: 'user5',
    createdTime: '2021-09-01T00:00:00Z',
    updatedTime: '2021-09-01T00:00:00Z',
    description: '',
  },
];

const initialState: {
  caseDetail: CaseDetailSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  caseDetail: {
    ...caseDetailInitialState,
    files: {
      status: 'complete',
      data: {
        results: mockFiles,
        totalResults: 50,
        limit: 50,
        from: 1,
        to: 50,
      },
    },
    viewType: ViewType.LIST,
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      evidenceTypeRegistryId: 'evidenceTypeRegistryId132',
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

const defaultProps = {
  selected: '',
  handleSelect: vi.fn(),
  handleDoubleClick: vi.fn(),
  handleUploadFile: vi.fn(),
  setSelectedFolderId: vi.fn(),
  selectedFolderId: '',
  setSelected: vi.fn(),
  pendingDeleteIds: [],
  setPendingDeleteIds: vi.fn(),
  pendingMoveFileIds: [],
};

const getCheckbox = (dataTestId: string) =>
  within(screen.getByTestId(dataTestId)).getByRole<HTMLInputElement>(
    'checkbox'
  );

describe('File Table', () => {
  vi.mock('@tanstack/react-virtual', () => ({
    useVirtualizer: vi.fn(() => ({
      getVirtualItems: () => [
        {
          index: 0,
          size: 69,
          start: 0,
          end: 69,
          key: 0,
          measureElement: vi.fn(),
        },
        {
          index: 1,
          size: 69,
          start: 69,
          end: 138,
          key: 1,
          measureElement: vi.fn(),
        },
        {
          index: 2,
          size: 69,
          start: 138,
          end: 207,
          key: 2,
          measureElement: vi.fn(),
        },
        {
          index: 3,
          size: 69,
          start: 207,
          end: 276,
          key: 3,
          measureElement: vi.fn(),
        },
        {
          index: 4,
          size: 69,
          start: 276,
          end: 345,
          key: 4,
          measureElement: vi.fn(),
        },
      ],
      getTotalSize: () => 69,
      measure: vi.fn(),
      scrollToIndex: vi.fn(),
    })),
  }));

  it('should render menu action correctly', async () => {
    const store = configureAppStore(initialState);
    store.dispatch = vi.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    const firstRowActionIcon = screen.getAllByTestId('search-table-menu')[0];

    userEvent.click(firstRowActionIcon);
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(screen.getByText('Edit Metadata')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });

    userEvent.click(screen.getByText('Edit Metadata'));
    expect(store.dispatch).toHaveBeenCalledWith(
      toggleOpenEditDrawer(mockFiles[0].id)
    );
  });

  it('shows grid view files when viewType is set to GRID', async () => {
    const gridState = clone({
      ...initialState,
      caseDetail: { ...initialState.caseDetail, viewType: ViewType.GRID },
    });
    const store = configureAppStore(gridState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.queryAllByTestId('file-card')[0]).toBeInTheDocument();
    });
  });

  it('shows list view files when viewType is set to LIST', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    await waitFor(() => {
      expect(screen.queryByTestId('file-card')).not.toBeInTheDocument();
    });
  });

  it('should blur file cards when the blur switch is toggled and the viewType is GRID', async () => {
    const gridState = clone({
      ...initialState,
      caseDetail: { ...initialState.caseDetail, viewType: ViewType.GRID },
    });
    const store = configureAppStore(gridState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    const blurSwitch = screen
      .getAllByTestId('blur-switch')[0]
      .querySelector('input');
    if (!blurSwitch) {
      throw new Error('Blur switch not found');
    }
    expect(blurSwitch).toBeInTheDocument();
    expect(blurSwitch?.checked).toBe(false);

    act(() => {
      blurSwitch.click();
    });

    expect(blurSwitch?.checked).toBe(true);
    expect(localStorage.getItem('investigate-set-blur')).toBe('true');

    await waitFor(() => {
      expect(
        screen.getAllByTestId('file-card-image')[0].children[0]
      ).toHaveClass('blurred');
    });
  });

  it('should render menu action correctly', async () => {
    const store = configureAppStore(initialState);
    store.dispatch = vi.fn();
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    const firstRowActionIcon = screen.getAllByTestId('search-table-menu')[0];

    userEvent.click(firstRowActionIcon);
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(screen.getByText('Edit Metadata')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });

    userEvent.click(screen.getByText('Edit Metadata'));
    expect(store.dispatch).toHaveBeenCalledWith(
      toggleOpenEditDrawer(mockFiles[0].id)
    );
  });

  it('should render circular progress when reload page', () => {
    const store = configureAppStore({
      ...initialState,
      caseDetail: {
        ...initialState.caseDetail,
        files: {
          status: 'idle',
          data: {
            results: [],
            totalResults: 0,
            limit: 50,
            from: 0,
            to: 0,
          },
        },
      },
    });
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('table-body_loading-icon')).toBeInTheDocument();
  });

  it('should show bulk delete dialog after event received and confirm delete', async () => {
    const getTdoDetailsSpy = vi
      .spyOn(GQLApi.prototype, 'getTdoDetails')
      .mockImplementationOnce(() =>
        Promise.resolve({
          tags: [],
          veritoneFile: {
            fileType: 'image',
          },
        })
      );

    const softDeleteFileSpy = vi
      .spyOn(GQLApi.prototype, 'softDeleteFile')
      .mockImplementationOnce(() =>
        Promise.resolve({
          folder: {
            id: 'folder_1',
            name: 'Mock Folder',
            description: 'Mock folder description',
            treeObjectId: 'tree_object_1',
            createdDateTime: '2021-09-01T00:00:00Z',
            modifiedDateTime: '2021-09-01T00:00:00Z',
            contentTemplates: [],
          },
        })
      );

    const store = configureAppStore(initialState);
    render(
      <ThemeProvider>
        <Provider store={store}>
          <MemoryRouter>
            <FileTable {...defaultProps} />
          </MemoryRouter>
        </Provider>
      </ThemeProvider>
    );

    act(() => {
      getCheckbox('check-box__check-row-file_1').click();
    });

    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-file_1')).toBeChecked();
    });

    act(() => {
      dispatchCustomEvent('delete-all-selected-files');
    });

    await waitFor(() => {
      const events = eventHistory.map(({ eventName }) => eventName);
      expect(events).toContain('delete-all-selected-files');
    });

    await waitFor(() => {
      expect(screen.queryByTestId('dialog')).toBeInTheDocument();

      const dialogElement = screen.getByTestId('dialog');

      expect(dialogElement).toHaveTextContent(
        'Deleting will immediately remove content from your Organization'
      );
    });

    act(() => {
      fireEvent.change(screen.getByTestId('type-confirmation-input'), {
        target: { value: 'delete-file' },
      });
    });

    await waitFor(() => {
      expect(screen.getByTestId('confirm-button')).not.toBeDisabled();
    });

    act(() => {
      screen.getByTestId('confirm-button')?.click();
    });

    await waitFor(() => {
      expect(getTdoDetailsSpy).toHaveBeenCalled();
    });

    await waitFor(() => {
      expect(softDeleteFileSpy).toHaveBeenCalled();
    });
  });

  it('should show bulk move dialog after event received', async () => {
    const store = configureAppStore(initialState);
    render(
      <ThemeProvider>
        <Provider store={store}>
          <MemoryRouter>
            <FileTable {...defaultProps} />
          </MemoryRouter>
        </Provider>
      </ThemeProvider>
    );

    act(() => {
      getCheckbox('check-box__check-row-file_1').click();
    });

    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-file_1')).toBeChecked();
    });

    act(() => {
      dispatchCustomEvent('move-all-selected-files');
    });

    await waitFor(() => {
      const events = eventHistory.map(({ eventName }) => eventName);
      expect(events).toContain('move-all-selected-files');
    });

    await waitFor(() => {
      expect(screen.queryByTestId('move-file-dialog')).toBeInTheDocument();
    });

    const dialogElement = screen.getByTestId('move-file-dialog');

    expect(dialogElement).toHaveTextContent('Move to Case');
  });
  it('should render CircularProgress when loading in GRID view', async () => {
    const gridState = clone({
      ...initialState,
      caseDetail: {
        ...initialState.caseDetail,
        viewType: ViewType.GRID,
        files: {
          status: 'loading',
          data: {
            results: [],
            totalResults: 0,
            limit: 50,
            from: 0,
            to: 0,
          },
        },
      },
    });
    const store = configureAppStore(gridState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    await waitFor(() => {
      expect(
        screen.getByTestId('file-table__card-view-loading')
      ).toBeInTheDocument();
    });
  });

  it('should select a row when clicking checkbox', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    const checkbox = getCheckbox('check-box__check-row-file_1');
    expect(checkbox).not.toBeChecked();
    fireEvent.click(checkbox);
    await waitFor(() => {
      expect(checkbox).toBeChecked();
    });
  });

  it('should select multiple rows with Shift key', async () => {
    const store = configureAppStore({
      ...initialState,
      caseDetail: {
        ...initialState.caseDetail,
        files: {
          ...initialState.caseDetail.files,
          data: {
            ...initialState.caseDetail.files.data,
            results: mockFiles,
          },
        },
      },
    });
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    ['file_1', 'file_2', 'file_3', 'file_4', 'file_5'].forEach((id) => {
      expect(getCheckbox(`check-box__check-row-${id}`)).not.toBeChecked();
    });
    fireEvent.click(getCheckbox('check-box__check-row-file_1'));
    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-file_1')).toBeChecked();
    });
    fireEvent.click(getCheckbox('check-box__check-row-file_5'), {
      shiftKey: true,
    });
    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-file_5')).toBeChecked();
    });
    await waitFor(() => {
      expect(getCheckbox('check-box__check-row-file_1')).toBeChecked();
      expect(getCheckbox('check-box__check-row-file_2')).toBeChecked();
      expect(getCheckbox('check-box__check-row-file_3')).toBeChecked();
      expect(getCheckbox('check-box__check-row-file_4')).toBeChecked();
      expect(getCheckbox('check-box__check-row-file_5')).toBeChecked();
    });
  });

  it('should unselect a row when unchecking its checkbox', async () => {
    const store = configureAppStore(initialState);
    render(
      <Provider store={store}>
        <MemoryRouter>
          <FileTable {...defaultProps} />
        </MemoryRouter>
      </Provider>
    );
    const checkbox = getCheckbox('check-box__check-row-file_1');
    expect(checkbox).not.toBeChecked();
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();
    fireEvent.click(checkbox);
    await waitFor(() => {
      expect(checkbox).not.toBeChecked();
    });
  });
});
