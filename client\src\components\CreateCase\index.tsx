import './index.scss';
import CaseCreatedModal from '@components/CreateCase/CreateSuccessModal';
import Dialog from '@components/Dialog';
import MultiSelect from '@components/MultiSelect';
import { useValidTags } from '@hooks';
import { I18nTranslate } from '@i18n';
import { Close as CloseIcon } from '@mui/icons-material';
import {
  Box,
  Button,
  FormControl,
  IconButton,
  InputAdornment,
  MenuItem,
  Select,
  TextField,
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { CaseTag } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  createNewCase,
  getCaseSDO,
  saveCase,
  selectCaseData,
  selectCaseStatus,
  selectCreateCase,
  selectCreateCaseStatus,
  selectEditingCaseFolderId,
  toggleCaseDrawer,
} from '@store/modules/caseManager/slice';
import {
  selectFetchedStatuses,
  selectFetchedTags,
  selectUpdatedCaseStatuses,
} from '@store/modules/settings/slice';
import { voidWrapper } from '@utils/helpers';
import { find } from 'lodash';
import { useEffect, useState } from 'react';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router';

interface CaseFormValues {
  caseName: string;
  caseId: string;
  description: string;
  caseDate: Date | null;
  caseStatusId: string;
  caseTagIds: string[];
}

const CreateEditCaseDrawer = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const intl = I18nTranslate.Intl();
  const fetchedStatuses = useSelector(selectFetchedStatuses);
  const fetchedTags = useSelector(selectFetchedTags);
  const updatedStatuses = useSelector(selectUpdatedCaseStatuses);
  const editingFolderId = useSelector(selectEditingCaseFolderId);
  const selectedCaseData = useSelector(selectCaseData);
  const createdCase = useSelector(selectCreateCase);
  const createdCaseStatus = useSelector(selectCreateCaseStatus);
  const selectedCaseStatus = useSelector(selectCaseStatus);

  const defaultStatusId = find(updatedStatuses, { active: true })?.id ?? '';
  const isLoading = createdCaseStatus === 'loading';

  const methods = useForm<CaseFormValues>({
    defaultValues: editingFolderId
      ? {
          caseName: selectedCaseData.caseName,
          caseId: selectedCaseData.caseId,
          description: selectedCaseData.description,
          caseDate: selectedCaseData.caseDate
            ? new Date(selectedCaseData.caseDate)
            : null,
          caseStatusId: selectedCaseData.statusId || defaultStatusId,
          caseTagIds: fetchedTags
            .filter((tag) =>
              selectedCaseData.preconfiguredTagIds?.includes(tag.id)
            )
            .map((tag) => tag.id),
        }
      : {
          caseName: '',
          caseId: '',
          description: '',
          caseDate: null,
          caseStatusId: defaultStatusId,
          caseTagIds: [],
        },
  });

  const {
    control,
    watch,
    handleSubmit,
    reset,
    formState: { isDirty, errors },
  } = methods;

  const [caseName, caseTagIds, description] = watch([
    'caseName',
    'caseTagIds',
    'description',
  ]);

  // const [caseRetentionPolicy, setCaseRetentionPolicy] = useState('3 Months'); // Future implementation
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [confirmDiscardOpen, setConfirmDiscardOpen] = useState(false);
  const { tags } = useValidTags(caseTagIds);

  const disableSubmit =
    !caseName || !!errors.caseName || !!errors.caseId || !isDirty;

  useEffect(() => {
    if (selectedCaseData && selectedCaseStatus === 'complete') {
      reset({
        caseName: selectedCaseData.caseName,
        caseId: selectedCaseData.caseId,
        description: selectedCaseData.description,
        caseDate: selectedCaseData.caseDate
          ? new Date(selectedCaseData.caseDate)
          : null,
        caseStatusId: selectedCaseData.statusId || defaultStatusId,
        caseTagIds: fetchedTags
          .filter((tag) =>
            selectedCaseData.preconfiguredTagIds?.includes(tag.id)
          )
          .map((tag) => tag.id),
      });
    }
    if (
      selectedCaseData.folderId !== editingFolderId &&
      selectedCaseStatus === 'loading'
    ) {
      reset({
        caseName: '',
        caseId: '',
        description: '',
        caseDate: null,
        caseStatusId: defaultStatusId,
        caseTagIds: [],
      });
    }
  }, [
    selectedCaseData,
    selectedCaseStatus,
    fetchedTags,
    editingFolderId,
    reset,
    defaultStatusId,
  ]);

  useEffect(() => {
    if (editingFolderId) {
      dispatch(getCaseSDO(editingFolderId));
    }
  }, [editingFolderId]);

  const handleCancel = () => {
    if (isDirty) {
      setConfirmDiscardOpen(true);
    } else {
      dispatch(toggleCaseDrawer());
    }
  };

  const handleSaveChanges = (formData: CaseFormValues) => {
    // create the investigate case object
    const investigateCase = {
      caseName: formData.caseName.trim(),
      caseId: formData.caseId.trim(),
      description: formData.description.trim(),
      caseDate: formData.caseDate?.toISOString().trim(),
      statusId: find(fetchedStatuses, { id: formData.caseStatusId })?.id,
      preconfiguredTagIds: formData.caseTagIds,
    };

    if (editingFolderId) {
      dispatch(
        saveCase({
          ...investigateCase,
          createdDateTime: selectedCaseData.createdDateTime,
          folderId: selectedCaseData.folderId,
          sdoId: selectedCaseData.sdoId,
          createdBy: selectedCaseData ? selectedCaseData.createdBy : '',
        })
      )
        .then(() => {
          dispatch(toggleCaseDrawer());
        })
        .catch((error) => {
          console.error('Failed to save case', error);
        });
    } else {
      dispatch(createNewCase(investigateCase))
        .then((result) => {
          if (result.meta.requestStatus === 'fulfilled') {
            setSaveSuccess(true);
          }
        })
        .catch((error) => {
          console.error('Failed to create case', error);
        });
    }
  };

  return (
    <FormProvider {...methods}>
      <div className="create-case" data-testid="create-case-container">
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <div className="create-case__title-container">
            <div
              className="create-case__heading-title"
              data-testid="create-case-title"
            >
              {editingFolderId
                ? I18nTranslate.TranslateMessage('editCase')
                : I18nTranslate.TranslateMessage('createNewCase')}
            </div>
            <div>
              <IconButton onClick={() => handleCancel()}>
                <CloseIcon />
              </IconButton>
            </div>
          </div>
          <Box role="presentation" className="create-case__main-container">
            <div className="create-case__case-name-id-container">
              <div className="create-case-name">
                <div className="create-case__field-title">
                  {I18nTranslate.TranslateMessage('caseNameWithAsterisk')}
                </div>
                <Controller
                  disabled={isLoading}
                  name="caseName"
                  control={control}
                  rules={{
                    required: 'Case name is required',
                    pattern: {
                      value: /^[a-zA-Z0-9\s_'-]+$/,
                      message: "Only letters, numbers, _ , '\ and - allowed",
                    },
                  }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                      slotProps={{
                        htmlInput: {
                          maxLength: 50,
                          'data-testid': 'case-name-textfield',
                        },
                      }}
                      fullWidth
                    />
                  )}
                />
              </div>
              <div className="create-case-id">
                <div className="create-case__field-title">
                  {I18nTranslate.TranslateMessage('caseID')}
                </div>
                <Controller
                  disabled={isLoading}
                  name="caseId"
                  control={control}
                  rules={{
                    pattern: {
                      value: /^[a-zA-Z0-9\s_-]+$/,
                      message: 'Only letters, numbers, _ , and - allowed',
                    },
                  }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                      slotProps={{
                        htmlInput: {
                          maxLength: 25,
                          'data-testid': 'case-id-textfield',
                        },
                      }}
                      fullWidth
                    />
                  )}
                />
              </div>
            </div>
            <div className="create-case__vertical-field-gap">
              <div className="create-case__field-title">
                {I18nTranslate.TranslateMessage('description')}
              </div>
              <Controller
                disabled={isLoading}
                name="description"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    multiline
                    rows={3}
                    slotProps={{
                      input: { className: 'create-case__description-input' },
                      htmlInput: {
                        'data-testid': 'case-description-textfield',
                        maxLength: 1000,
                      },
                    }}
                    fullWidth
                  />
                )}
              />
              <div
                className="create-case__description-count"
                data-testid="description-count"
              >
                {description.length}
              </div>
            </div>
            <div className="create-case__case-status-date-container">
              <div className="create-case__vertical-field-gap">
                <div className="create-case__field-title">
                  {I18nTranslate.TranslateMessage('caseStatus')}
                </div>
                <FormControl
                  className="create-case__status-form-field"
                  data-testid="case-status-list"
                >
                  <Controller
                    name="caseStatusId"
                    control={control}
                    render={({ field }) => (
                      <Select
                        disabled={isLoading}
                        {...field}
                        renderValue={(selected) => {
                          const status = find(fetchedStatuses, {
                            id: selected,
                          });
                          return (
                            <Box
                              className="select__value"
                              display="flex"
                              alignItems="center"
                              justifyContent="space-between"
                            >
                              <div>{status ? status.label : ''}</div>
                              {status && status?.label !== 'No Status' && (
                                <InputAdornment position="end">
                                  <CloseIcon
                                    className="create-case__clear-icon"
                                    onMouseDown={(e) => {
                                      e.stopPropagation();
                                      field.onChange('no status');
                                    }}
                                  />
                                </InputAdornment>
                              )}
                            </Box>
                          );
                        }}
                      >
                        {fetchedStatuses.map((status) =>
                          status.active ? (
                            <MenuItem key={status.id} value={status.id}>
                              {status.label}
                            </MenuItem>
                          ) : null
                        )}
                      </Select>
                    )}
                  />
                </FormControl>
              </div>
              <div className="create-case__vertical-field-gap">
                <div className="create-case__field-title">
                  {I18nTranslate.TranslateMessage('caseDate')}
                </div>
                <FormControl
                  className="create-case__date-picker-form-field"
                  data-testid="date-selector-form"
                >
                  <Controller
                    name="caseDate"
                    control={control}
                    render={({ field }) => (
                      <DatePicker
                        disabled={isLoading}
                        disableFuture
                        openTo="day"
                        value={field.value}
                        maxDate={new Date()} // Today and before
                        onChange={(date) => field.onChange(date)}
                        data-testid="case-date-datepicker"
                      />
                    )}
                  />
                </FormControl>
              </div>
            </div>
            {/* <div className="create-case__vertical-field-gap"> */}
            {/*  <div className="create-case__field-title-disabled"> */}
            {/*    {I18nTranslate.TranslateMessage('caseRetentionPolicy')} */}
            {/*  </div> */}
            {/*  <Select */}
            {/*    data-testid="case-retention-policy-select" */}
            {/*    value={caseRetentionPolicy} */}
            {/*    onChange={({ target: { value } }) => */}
            {/*      setCaseRetentionPolicy(value) */}
            {/*    } */}
            {/*    style={{ width: '50%' }} */}
            {/*    disabled // Revisit this in later phase */}
            {/*  > */}
            {/*    <MenuItem value={'3 Months'}>{'3 Months'}</MenuItem> */}
            {/*    <MenuItem value={'6 Months'}>{'6 Months'}</MenuItem> */}
            {/*    <MenuItem value={'9 Months'}>{'9 Months'}</MenuItem> */}
            {/*    <MenuItem value={'1 Year'}>{'1 Year'}</MenuItem> */}
            {/*  </Select> */}
            {/* </div> */}
            <div className={'create-case__vertical-field-gap'}>
              <div className={'create-case__field-title'}>
                {I18nTranslate.TranslateMessage('selectUpToTenTags')}
              </div>
              <MultiSelect<CaseTag>
                options={tags}
                currentIds={caseTagIds}
                isDisabled={isLoading}
                name="caseTagIds"
              />
            </div>
          </Box>
          <div className={'create-case__footer-container'}>
            <div className={'create-case__action-button-container'}>
              <div>
                <Button
                  onClick={handleCancel}
                  color="inherit"
                  className="create-case__cancel-button"
                  data-testid="create-edit-case-cancel-button"
                  disabled={isLoading}
                >
                  {I18nTranslate.TranslateMessage('cancel')}
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  loadingPosition="start"
                  loading={isLoading}
                  onClick={voidWrapper(handleSubmit(handleSaveChanges))}
                  disabled={disableSubmit}
                  style={{ textTransform: 'none' }}
                  sx={{
                    '&.MuiButton-root': {
                      padding: '6px 16px',
                    },
                  }}
                  data-testid="create-edit-case-save-button"
                >
                  {editingFolderId
                    ? I18nTranslate.TranslateMessage('save')
                    : I18nTranslate.TranslateMessage('createCase')}
                </Button>
              </div>
            </div>
          </div>
        </LocalizationProvider>
        <CaseCreatedModal
          showCaseCreatedModal={saveSuccess}
          closeCaseCreatedModal={() => {
            setSaveSuccess(false);
            dispatch(toggleCaseDrawer());
          }}
          onCloseOKAndGoToNewCase={() => {
            setSaveSuccess(false);
            dispatch(toggleCaseDrawer());
            if (createdCase.id) {
              navigate(`/case-manager/${createdCase.id}`);
            }
          }}
        />
        <Dialog
          open={confirmDiscardOpen}
          onClose={() => {
            setConfirmDiscardOpen(false);
          }}
          title={intl.formatMessage({ id: 'areYouSure' })}
          onConfirm={() => {
            setConfirmDiscardOpen(false);
            dispatch(toggleCaseDrawer());
          }}
          disableConfirm={false}
          confirmText={intl.formatMessage({ id: 'discard' })}
          cancelText={intl.formatMessage({ id: 'cancel' })}
        >
          <div>{I18nTranslate.TranslateMessage('confirmDiscardChanges')}</div>
        </Dialog>
      </div>
    </FormProvider>
  );
};

export default CreateEditCaseDrawer;
