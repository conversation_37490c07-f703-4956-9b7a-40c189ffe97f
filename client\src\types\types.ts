import { CognitionItem } from '@components/CognitionAutocomplete';
import { TdoDescription } from '@store/modules/search/getDescriptions';

export interface InvestigateCase {
  caseName: string;
  caseId?: string;
  description?: string;
  caseDate?: string;
  statusId?: string;
  preconfiguredTagIds?: string[];
  folderId?: string;
  createdBy?: string;
  createdByName?: string;
  modifiedBy?: string;
}

export type CaseSDO = InvestigateCase & {
  createdDateTime: string;
  modifiedDateTime: string;
  toBeDeletedTime?: string;
};

export type InvestigateCaseSDO = CaseSDO & {
  sdoId: string;
};

export interface CaseSearchResults {
  searchMedia: {
    jsondata: {
      results: Array<{
        caseId: string;
        statusId: string;
        caseDate: string;
        caseName: string;
        folderId: string;
        createdBy: string;
        description: string;
        createdDateTime: string;
        modifiedDateTime: string;
        preconfiguredTagIds: string[];
        id: string;
      }>;
      totalResults: number;
      limit: number;
      from: number;
      to: number;
    };
  };
}

export interface CaseStatus {
  id: string;
  label: string;
  color: string;
  active: boolean;
}

export interface FileStatus {
  id: string;
  label: string;
}

export interface CaseTag {
  id: string;
  label: string;
  active: boolean;
}

export type PreconfiguredTags = CaseTag[];

export interface Folder {
  folder: {
    id: string;
    name: string;
    description?: string;
    treeObjectId: string;
    createdDateTime: string;
    modifiedDateTime: string;
    contentTemplates: {
      id: string;
      sdo: {
        id: string;
        schemaId: string;
        data: CaseSDO;
      };
    }[];
  };
}

export interface User {
  user: {
    id: string;
    email: string;
    lastName: string;
    firstName: string;
  };
}

export interface BasicUserInfo {
  basicUserInfo: User['user'];
}

export interface SearchMedia<R = unknown> {
  searchMedia: {
    jsondata: {
      results: Array<R>;
      totalResults: {
        value: number;
      };
      limit: number;
      from: number;
      to: number;
    };
  };
}

export type FILES_SORT_FIELD =
  | 'veritone-file.filename'
  | 'createdTime'
  | 'fileType'
  | 'dateUploaded';
export type FILES_SORT_DIRECTION = 'desc' | 'asc';

export interface SearchMediaCondition {
  operator: string;
  field?: string;
  value?: string;
  values?: string[];
  not?: boolean;
  gte?: string;
  lte?: string;
  query?: {
    operator: string;
    field: string;
    value?: string;
    dotNotation: boolean;
    gte?: string;
  };
  conditions?: SearchMediaCondition[];
}

// TODO: Rename since there is a JS File interface in the browser
export interface VFile {
  id: string;
  fileName: string;
  fileType: string;
  parentTreeObjectIds: string[];
  createdByName: string;
  createdTime: string;
  updatedTime: string;
  programLiveImage?: string;
  caseId?: string;
  description?: string;
}

export interface TemporalDataObject {
  id: string;
  name: string;
  description: string | null;
  contentType: string;
  parentFolderId?: string;
  primaryAsset?: {
    contentType: string;
    signedUri?: string;
  };
  details?: {
    tags?: Record<string, string>[];
    veritoneFile?: {
      fileName?: string;
      fileType?: string;
    };
  };
  streams?: {
    protocol: string;
    uri: string;
  }[];
}

export interface CaseFilterValue {
  caseName?: string;
  caseId?: string;
  statusId?: string;
  tagIds?: string[];
}

export interface FilterFormValue {
  search: string;
  metadata: string; // todo: don't know if this is needed
  // case information
  caseStatus: string;
  filterCaseTagIds: string[];
  caseId: string;
  uploadDate: {
    startDate: string;
    endDate: string;
  };
  retentionDate?: string; // todo: replace with required field when filter retention date is implemented
  fileStatus: string;
  // advanced search
  filename: string;
  wordsInTranscription: string;
  faceDetections: CognitionItem[];
  objectDescriptors: CognitionItem[];
  vehicleRecognition: string;
  licensePlateRecognition: string;
  // sceneClassification: string;
  textRecognition: string;
  // evidence type
  callRecording: boolean;
  arrestReport: boolean;
  bodyWornCamera: boolean;
  bookingPhoto: boolean;
  citizenSubmittedVideo: boolean;
  crimeScenePhoto: boolean;
  inCarVideo: boolean;
  interviewAudioRecording: boolean;
  interviewRoomRecording: boolean;
  mobileDeviceExtraction: boolean;
  securityCameraVideo: boolean;
  // file type
  photo: boolean;
  video: boolean;
  audio: boolean;
  document: boolean;
  image: boolean;
}

export interface EngineResult {
  tdoId: string;
  engineId: string;
  startOffsetMs?: number;
  stopOffsetMs?: number;
  assetId: string;
  userEdited?: boolean;
  jsondata: {
    sourceEngineId: string;
    modifiedDateTime: number;
    summary?: string;
    object?: { type: string; text: string }[];
    series?: {
      startTimeMs: number;
      stopTimeMs: number;
      speakerId?: string;
      object?: {
        type: string;
        label?: string;
        libraryId?: string;
        confidence?: number;
        text?: string;
        boundingPoly?: { x: number; y: number }[];
        uri?: string;
        entityId?: string;
      }[];
      words?: {
        word: string;
        confidence: number;
        bestPath?: boolean;
        utteranceLength?: number;
      }[];
    };
  };
}

export interface ErrorGQLResponse {
  message: string;
  name: string;
  time_thrown: string;
  data: {
    objectType: string;
    objectId: string;
    organizationId: number;
    userId: string;
    errorId: string;
    requestId: string;
    correlationId: string;
  };
  path: string[];
  locations: {
    line: number;
    column: number;
  }[];
}

export interface TdoDescriptions {
  temporalDataObjects: {
    count: number;
    limit: number;
    offset: number;
    records: Array<TdoDescription>;
  };
}

export interface Folders {
  data: {
    [key: string]:
      | {
          id: string;
          name: string;
          createdDateTime: string;
        }
      | null
      | ErrorGQLResponse;
  };
  errors?: ErrorGQLResponse[];
}
export interface RecordingMetadata {
  temporalDataObject: {
    id: string;
    primaryAsset: {
      id: string;
      jsondata: {
        size: number;
        mediaDuration: number | null;
        schemaId: string;
      };
    } | null;
    assets: {
      records: Array<{
        id: string;
        jsondata: {
          size: number;
          schemaId: string;
        };
        transform: string;
      }>;
    };
    jobs: {
      records: Array<{
        id: string;
        status:
          | 'pending'
          | 'queued'
          | 'running'
          | 'complete'
          | 'failed'
          | 'cancelled'
          | 'aborted';
      }>;
    };
    createdBy?: string;
    createdByName?: string;
    createdDateTime: string;
    name: string;
    description: string | null;
    details: {
      name: string;
      addToIndex: boolean;
      veritonePermissions: {
        acls: Array<{
          groupId: string;
          permission: string;
        }>;
        isPublic: boolean;
      };
      veritoneFile: {
        size: number;
        fileName: string;
        filename: string;
        mimetype: string;
      };
    };
    folders: Array<{
      contentTemplates: Array<{
        sdo: {
          id: string;
          data:
            | {
                sdoId: string;
                caseId: string;
                status: string; // Deprecated
                statusId: string;
                caseDate: string;
                caseName: string;
                folderId: string;
                createdBy: string;
                modifiedBy: string;
                description: string;
                createdDateTime: string;
                modifiedDateTime: string;
                preconfiguredTagIds: Array<string>;
              }
            | undefined;
        };
      }>;
    }>;
  };
}
export interface OwnerInfo {
  createdBy: string;
  createdByName?: string;
}
