{"type": "object", "title": "investigate-case", "required": ["caseName", "folderId", "created<PERSON>y", "modifiedBy", "createdDateTime", "modifiedDateTime"], "properties": {"tags": {"type": "array", "items": {"type": "string"}}, "caseId": {"type": "string", "maxLength": 25}, "statusId": {"type": "string"}, "caseDate": {"type": "dateTime"}, "caseName": {"type": "string", "maxLength": 50}, "folderId": {"type": "string"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "description": {"type": "string"}, "createdDateTime": {"type": "dateTime"}, "toBeDeletedTime": {"type": "dateTime"}, "modifiedDateTime": {"type": "dateTime"}, "preconfiguredTagIds": {"type": "array", "items": {"type": "string"}}}, "description": "Investigate Case"}