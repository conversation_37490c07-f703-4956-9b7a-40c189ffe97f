{"version": "0.2", "language": "en,es,fr", "words": ["aiware", "apiroot", "caseId", "caseName", "cellebrite", "codecept", "fileinput", "firstname", "formatjs", "fulltext", "githubusercontent", "gstatic", "intercategory", "intracategory", "jambit", "jsondata", "lastname", "mkdirp", "noti", "notistack", "ocrtext", "picklist", "playout", "preconfigured", "sdos", "setstate", "streetsidesoftware", "Tagbox", "textnodes", "tsmerge", "Unskip", "veritone", "Videocam", "visualstudio", "vsmarketplacebadge", "xxlarge"], "flagWords": []}