import './index.scss';
import { EditAttributes } from '@assets/icons';
import CaseStatusMenu from '@components/CaseStatus';
import { I18nTranslate } from '@i18n';
import {
  Button,
  CircularProgress,
  IconButton,
  Paper,
  Tab,
  Tabs,
} from '@mui/material';
import { ContentType, EvidenceType } from '@shared-types/metadata';
import { useAppDispatch } from '@store/hooks';
import {
  getCaseSDO,
  saveCase,
  selectCaseData,
  selectCaseStatus,
  toggleCaseDrawer,
} from '@store/modules/caseManager/slice';
import { selectMetadataByFolderId } from '@store/modules/metadata/slice';
import { selectFetchedTags } from '@store/modules/settings/slice';
import { SyntheticEvent, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import EmptyState from './EmptyState';

export const CONTENT_TYPES = [
  'video' as const,
  'audio' as const,
  'image' as const,
  'document' as const,
];
const EVIDENCE_TYPES = [
  '911 Call Recording' as const,
  'Arrest Report' as const,
  'Body Worn Camera' as const,
  'Booking Photo' as const,
  'Citizen Submitted Video' as const,
  'Crime Scene Photo' as const,
  'In Car Video' as const,
  'Interview Audio Recording' as const,
  'Interview Room Recording' as const,
  'Mobile Device Extraction' as const,
  'Security Camera Video' as const,
  'Un-Assigned' as const,
];

const PLACE_HOLDER = '--';

const CaseDetails = ({ folderId, showCaseFiles, onShowCaseFiles }: Props) => {
  const dispatch = useAppDispatch();

  const caseDetailStatus = useSelector(selectCaseStatus);
  const caseDetailData = useSelector(selectCaseData);
  const {
    caseName,
    caseDate,
    createdBy,
    createdByName,
    caseId,
    statusId,
    description,
    preconfiguredTagIds,
    sdoId,
    createdDateTime,
  } = caseDetailData || {};
  const fetchPreconfiguredTags = useSelector(selectFetchedTags);
  const metadataByFolderId = useSelector(selectMetadataByFolderId);

  const [tabValue, setTabValue] = useState(0);
  const handleTabChange = (_event: SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const hasValidProps = caseName && folderId;

  const caseTags = useMemo(() => {
    const tagSet = new Set(preconfiguredTagIds);
    return fetchPreconfiguredTags.filter(
      (tag) => tagSet.has(tag.id) && tag.active
    );
  }, [preconfiguredTagIds, fetchPreconfiguredTags]);

  const isMetadataLoading =
    folderId && metadataByFolderId?.[folderId]?.status === 'loading';

  const metadataLookup = useMemo(() => {
    if (
      folderId &&
      folderId in metadataByFolderId &&
      metadataByFolderId[folderId].status === 'complete'
    ) {
      const defaultMetadataLookup: Record<
        ContentType | EvidenceType | 'Un-Assigned',
        number
      > = {
        video: 0,
        audio: 0,
        image: 0,
        document: 0,
        '911 Call Recording': 0,
        'Arrest Report': 0,
        'Body Worn Camera': 0,
        'Booking Photo': 0,
        'Citizen Submitted Video': 0,
        'Crime Scene Photo': 0,
        'In Car Video': 0,
        'Interview Audio Recording': 0,
        'Interview Room Recording': 0,
        'Mobile Device Extraction': 0,
        'Security Camera Video': 0,
        'Un-Assigned': 0,
      };

      Object.values(metadataByFolderId[folderId].data).forEach((tdo) => {
        const evidenceType =
          tdo.evidenceType === '' ? 'Un-Assigned' : tdo.evidenceType;
        defaultMetadataLookup[tdo.contentType] =
          (defaultMetadataLookup[tdo.contentType] ?? 0) + 1;
        defaultMetadataLookup[evidenceType] =
          (defaultMetadataLookup[evidenceType] ?? 0) + 1;
      });

      return defaultMetadataLookup;
    }

    return undefined;
  }, [folderId, metadataByFolderId]);

  const handleEditCase = () => {
    if (folderId) {
      dispatch(toggleCaseDrawer(folderId));
    }
  };

  const handleSaveCaseStatus = (statusId: string) => {
    if (hasValidProps) {
      dispatch(
        saveCase({
          caseName,
          caseId,
          description,
          caseDate,
          statusId,
          preconfiguredTagIds,
          createdBy,
          folderId,
          createdDateTime,
          sdoId,
        })
      );
    }
  };

  useEffect(() => {
    if (folderId) {
      dispatch(getCaseSDO(folderId));
    }
  }, [folderId]);

  const groupIconButton = () => (
    <div className="group-icon-buttons">
      <IconButton
        className="icon-button"
        onClick={handleEditCase}
        data-testid="edit-case-button"
      >
        <EditAttributes />
      </IconButton>
      {/* <Button className="icon-button"> */}
      {/*  <Share />*/}
      {/* </Button> */}
    </div>
  );

  return (
    <Paper
      className="case-details"
      style={{ display: folderId ? 'block' : 'none' }}
      elevation={0}
    >
      {caseDetailStatus === 'loading' ? (
        <div className="loading-icon">
          <CircularProgress />
        </div>
      ) : hasValidProps ? (
        <>
          {showCaseFiles && (
            <div className="case-details-tabs" data-testid="case-details-tabs">
              <Tabs
                onChange={handleTabChange}
                value={tabValue}
                indicatorColor="primary"
              >
                <Tab
                  label={I18nTranslate.TranslateMessage('overview')}
                  disabled
                />
                {/* Please leave this commented out for now.  Shares will be added in a future release.*/}
                {/* <Tab*/}
                {/*  label={I18nTranslate.TranslateMessage('shares')}*/}
                {/*  disabled*/}
                {/* />*/}
              </Tabs>
            </div>
          )}
          <div
            className="case-details__content"
            data-testid="case-details-content"
          >
            {!showCaseFiles && (
              <div className="buttons-container">
                <Button
                  className="text-button"
                  data-testid="view-case-details-button"
                  onClick={onShowCaseFiles}
                >
                  {I18nTranslate.TranslateMessage('viewCaseDetails')}
                </Button>
                {groupIconButton()}
              </div>
            )}
            <div className="case__title">
              <div className="case-name" data-testid="case-name">
                {caseName}
              </div>
              {showCaseFiles && groupIconButton()}
            </div>
            <div className="case-id">
              {I18nTranslate.TranslateMessage('caseIdWithColons')}
              {caseId || PLACE_HOLDER}
            </div>
            <div className="case-status">
              <CaseStatusMenu
                currentStatusId={statusId}
                onSaveStatus={handleSaveCaseStatus}
                currentRowId={folderId}
              />
            </div>
            <div className="case-dates">
              <div style={{ display: 'flex', gap: 12 }}>
                <div className="case-detail-bold">
                  {I18nTranslate.TranslateMessage('caseDateWithColons')}
                </div>
                <div className="case-detail">
                  {caseDate
                    ? new Date(caseDate).toISOString().split('T')[0]
                    : PLACE_HOLDER}
                </div>
              </div>
              {/* <div style={{ display: 'flex', gap: 12 }}> */}
              {/*  <div className="case-detail-bold"> */}
              {/*    {I18nTranslate.TranslateMessage('retentionDate')}: */}
              {/*  </div> */}
              {/*  <div className="case-detail"> */}
              {/*    {mockCaseDetails.retentionDate */}
              {/*      ? new Date(mockCaseDetails.retentionDate) */}
              {/*          .toISOString() */}
              {/*          .split('T')[0] */}
              {/*      : PLACE_HOLDER} */}
              {/*  </div> */}
              {/* </div> */}
            </div>
            <div className="case-owner">
              <div className="case-detail-bold">
                {I18nTranslate.TranslateMessage('caseOwnerWithColons')}
              </div>
              <div className="case-detail" data-testid="case-detail-user-name">
                {createdByName}
              </div>
            </div>
            {description && (
              <div className="case-description">{description}</div>
            )}
            {caseTags.length > 0 && (
              <div className="case-tags">
                {caseTags.map((tag) => (
                  <div
                    key={tag.id}
                    className="case-tag"
                    data-testid={`case-tag-${tag.id}`}
                  >
                    {tag.label}
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="case-evidence">
            <div className="case-evidence-title">
              {I18nTranslate.TranslateMessage('contentTypesWithColon')}
            </div>
            <ul className="case-evidence-list">
              {isMetadataLoading ? (
                <div className="case-evidence-loading ">
                  <CircularProgress size={20} />
                </div>
              ) : metadataLookup ? (
                CONTENT_TYPES.map((type) => (
                  <li key={type} className="case-evidence-list-item">
                    {I18nTranslate.TranslateMessage('case-evidence-list-item', {
                      type,
                      count: metadataLookup?.[type] ?? 0,
                    })}
                  </li>
                ))
              ) : (
                <div className="case-evidence-list-item">
                  {I18nTranslate.TranslateMessage('metadataCouldNotBeFound')}
                </div>
              )}
            </ul>
            <div className="case-evidence-title">
              {I18nTranslate.TranslateMessage('evidenceTypesWithColon')}
            </div>
            <ul className="case-evidence-list">
              {isMetadataLoading ? (
                <div className="case-evidence-loading ">
                  <CircularProgress size={20} />
                </div>
              ) : metadataLookup ? (
                EVIDENCE_TYPES.map((type) => (
                  <li key={type} className="case-evidence-list-item">
                    {I18nTranslate.TranslateMessage('case-evidence-list-item', {
                      type,
                      count: metadataLookup?.[type] ?? 0,
                    })}
                  </li>
                ))
              ) : (
                <div className="case-evidence-list-item">
                  {I18nTranslate.TranslateMessage('metadataCouldNotBeFound')}
                </div>
              )}
            </ul>
          </div>
        </>
      ) : (
        <EmptyState
          title={I18nTranslate.TranslateMessage('someThingWentWrong')}
          description={I18nTranslate.TranslateMessage('unableToLoadData', {
            br: <br key="line-break " />,
          })}
        />
      )}
    </Paper>
  );
};

interface Props {
  folderId?: string;
  showCaseFiles: boolean;
  onShowCaseFiles: () => void;
}

export default CaseDetails;
