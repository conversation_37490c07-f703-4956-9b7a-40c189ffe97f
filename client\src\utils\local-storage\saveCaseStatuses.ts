export interface CaseStatus {
  id: string;
  statusId: string | undefined;
  expiresAt: number;
}

const LOCAL_STORAGE_KEY = 'caseStatuses';

export const getCaseStatuses = (): CaseStatus[] => {
  // TODO: Type of caseStatuses should be validated
  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
  const caseStatuses = JSON.parse(
    localStorage.getItem(LOCAL_STORAGE_KEY) || '[]'
  );
  // TODO: We should be validating the structure of the data, not just parsing and assuming it's correct.
  return Array.isArray(caseStatuses) ? (caseStatuses as CaseStatus[]) : [];
};

export const setCaseStatuses = (statuses: CaseStatus[]): void => {
  localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(statuses));
};

export const cleanUpExpiredStatuses = (): CaseStatus[] => {
  const now = Date.now();
  const validStatuses = getCaseStatuses().filter(
    (status) => status.expiresAt > now
  );

  setCaseStatuses(validStatuses);

  return validStatuses;
};

export const addCaseStatusToLocalStorage = (newStatus: CaseStatus): void => {
  const statuses = cleanUpExpiredStatuses();
  const existingStatusIndex = statuses.findIndex(
    (status) => status.id === newStatus.id
  );
  if (existingStatusIndex !== -1) {
    const update = {
      id: statuses[existingStatusIndex].id,
      statusId: newStatus.statusId,
      expiresAt: newStatus.expiresAt,
    };

    statuses[existingStatusIndex] = update;
  } else {
    statuses.push(newStatus);
  }
  setCaseStatuses(statuses);
};
