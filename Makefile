RC_URL=registry.central.aiware.com

# If git commit is not set, get it from FS
ifeq ($(GIT_COMMIT),)
	GIT_COMMIT=$(shell git rev-parse HEAD)
endif

build: build-investigate

build-investigate:
	docker build -t investigate-app:${GIT_COMMIT} -f ./Dockerfile --build-arg K8S_BUILD=FALSE --build-arg APPLICATION=investigate-app --build-arg GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} .
	docker tag investigate-app:${GIT_COMMIT} investigate-app:latest

run:
	docker run -p 9000:9000 -e ENVIRONMENT=stage -e APPLICATION=investigate-app -e GITHUB_ACCESS_TOKEN=${GITHUB_ACCESS_TOKEN} investigate-app 

publish: publish-investigate

publish-investigate:
	$(MAKE) docker-tag SERVICE=investigate-app
	$(MAKE) docker-push SERVICE=investigate-app

docker-tag:
	echo "Tagging ${SERVICE} with Registry Central registry ${RC_URL}"
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:latest
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker tag ${SERVICE}:${GIT_COMMIT} ${RC_URL}/${SERVICE}:dev

docker-push:
	echo "Pushing ${SERVICE} to ${RC_URL}"
	docker push ${RC_URL}/${SERVICE}:latest
	docker push ${RC_URL}/${SERVICE}:${GIT_COMMIT}
	docker push ${RC_URL}/${SERVICE}:dev
