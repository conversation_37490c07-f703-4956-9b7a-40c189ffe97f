import { getOwner } from '@utils/getOwners';
import { CognitionItem } from '@components/CognitionAutocomplete';
import { I18nTranslate } from '@i18n';
import { PayloadAction } from '@reduxjs/toolkit';
import {
  FILES_SORT_DIRECTION,
  FILES_SORT_FIELD,
  FilterFormValue,
} from '@shared-types/types';
import { createAppSlice } from '@store/createAppSlice';
import HttpClient from '@store/dependencies/httpClient';
import { RootState } from '@store/index';
import { ApiStatus } from '@store/types';
import { getApiAuthToken } from '@store/utils';
import { fromEntries, GQLApi } from '@utils/helpers';
import {
  Category,
  getBlurValue,
  getCategoriesLocalStorage,
  getHideIfNoResultsLocalStorage,
  getSearchViewLocalStorage,
  getSortCategoryLocalStorage,
  getViewTypeLocalStorage,
  isEqualCategories,
  isSearchView,
  isSortCategory,
  SearchView,
  setBlurValueLocalStorage,
  setCategoriesLocalStorage,
  setHideIfNoResultsLocalStorage,
  setSearchViewLocalStorage,
  setSortCategoryLocalStorage,
  setViewTypeLocalStorage,
  SortCategory,
  ViewType,
} from '@utils/local-storage';
import { SearchMediaQueryArgs } from '@veritone/glc-redux';
import { isEmpty } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import { getDescriptions } from './getDescriptions';
import {
  AutocompleteResponse,
  queryEntitySearch,
  queryLibrarySearch,
  queryObjectSearch,
  searchFiles as searchFilesFn,
  SearchMediaResponse,
} from './searchFiles';

export interface SelectedRow {
  id: string;
  caseId?: string;
}

export const resultCategories: string[] = [
  'filename',
  'transcription',
  'faceRecognition',
  'objectDetection',
  'vehicleRecognition',
  'licensePlateRecognition',
  // 'sceneClassification',
  'textRecognition',
  'metadata',
] as const;
export type ResultCategory = (typeof resultCategories)[number];

export type SearchResultType = 'grouped' | 'ungrouped';

export type SortType = 'fileName' | 'dateUploaded'; // caseId may not be possible or realistic to sort here

export type SortOrder = 'asc' | 'desc';

export type EvidenceType =
  | '911 Call Recording'
  | 'Arrest Reporting'
  | 'Body Worn Camera'
  | 'Booking Photo'
  | 'Citizen Submitted Video'
  | 'Crime Scene Photo'
  | 'In Car Video'
  | 'Interview Audio Recording';

// TODO: Should 'Photo' actually be here?
// export type FileType = 'Image' | 'Video' | 'Audio' | 'Document';
export type FileType = 'Image' | 'Video' | 'Audio' | 'Document' | 'Photo';

export enum MathOperators {
  AND = 'and',
  OR = 'or',
  OPEN_PARENTHESIS = '(',
  CLOSE_PARENTHESIS = ')',
}
export interface FaceDetectionTerm {
  type: 'string' | 'entity' | 'library' | 'operator';
  operator?: MathOperators;
  entityId?: string;
  libraryId?: string;
  // When type=string, This will do a search for all matching(?... or top x?) entities or libraries that match the string, and then do a search
  stringQuery?: string;
}
export interface ObjectDetectionTerm {
  type: 'foundFullTextString' | 'foundString' | 'operator';
  operator?: MathOperators;
  foundFullTextString?: string;
  foundString?: string;
}
export interface FileSearchParams {
  // This is the generic search at the top of the search box, searchResultType, checkedResultCategories change result output
  keywordSearchQuery?: string;
  // This either search each cognition type independently or all together
  // Grouped will have a loading state per cognition type
  searchResultType: SearchResultType;
  // This is the list of sorted cognition types that are checked in the top right menu
  checkedResultCategories: ResultCategory[];
  sort?: {
    type: FILES_SORT_FIELD;
    order: FILES_SORT_DIRECTION;
  };
  // pagination settings for each result category, isUpdate will cause only the categories listed as isUpdate to run
  pagination: Partial<
    Record<
      // TODO: ResultCategory is just string, should it be more specific?
      // ResultCategory | 'ungrouped',
      ResultCategory,
      { offset: number; limit: number; isUpdate?: boolean }
    >
  >;
  // These are a list of IDs stored in the status SDO that represent different user defined case statuses
  caseStatusIdsFilter?: string[]; // TODO
  caseTagIdsFilter?: string[];
  caseIdFilter?: string[];
  uploadDateFilter?: {
    startDate: string;
    endDate: string;
  };
  fileStatusFilter?: string[]; // TODO
  cognitionFilters?: {
    // search for filenames
    filename?: string;
    // searches for words in the transcript in the main index field transcript.transcript
    wordsInTranscript?: string;
    faceDetection?: {
      // A term is part of the search that is either an entity, string, library, or operator.
      // Each string, entity, or library must be separated by an operator.
      // Operator '(' must be closed with a second operator ')'
      // entityIds search in the main index on the field face-recognition.series.entityId
      // libraryIds search in the main index on the field face-recognition.series.libraryId
      // stringQuery will search for all entities or libraries that match the string and search on the above path

      terms: FaceDetectionTerm[];
    };
    objectDetection?: {
      // A term is part of the search that is either an found string, full text string, or operator.
      // Each foundFullTextString, foundString, or library must be separated by an operator.
      // Operator '(' must be closed with a second operator ')'
      // When type=foundString, this will do a search in the main index for a value in the field 'object-recognition.series.found'
      // When type=foundFullTextString, this will do a search in the main index for an open ended value (eg. value: "*test*") in the field 'object-recognition.series.found.fulltext'
      terms: ObjectDetectionTerm[];
    };
    // Leaving as a string for now until we get the license plate engine fixed, which outputs vehicles as well as plates. This may be able to stay a string
    // TODO: note on field lookup after index fixed
    vehicleDetection?: string;
    // A string representing a license plate
    // TODO: note on field lookup after index fixed
    licensePlateDetection?: string;
    // Searches label based on the output from Tagbox engine, eg. labels like 'Sport venue' or 'Crowd'
    // sceneDetection?: string;
    // Searches the 'text-recognition.series.ocrtext' field for text recognition
    textRecognition?: string;
    // a string that will search all metadata fields
    metadata?: string;
  };
  // array of evidence types checked
  evidenceTypeFilter?: EvidenceType[];
  // array of file types types checked
  fileTypeFilter?: FileType[];
}

export interface QueryObjectSearchParams {
  keywordSearchQuery?: string;
  pagination: Partial<
    Record<
      // TODO: ResultCategory is just string, should it be more specific?
      // ResultCategory | 'ungrouped',
      ResultCategory,
      { offset: number; limit: number }
    >
  >;
  objectDetection?: {
    terms: ObjectDetectionTerm[];
  };
}
export type ParamTermInvalidReason =
  | 'unmatched_parentheses'
  | 'no_operator_between_parentheses'
  | 'operator_at_wrong_position'
  | 'no_operator_found_at_position';

export interface SearchResults {
  // TODO: Should aborted be a status?
  // status: 'idle' | 'loading' | 'success' | 'failure';
  status: 'idle' | 'loading' | 'success' | 'failure' | 'aborted';
  pagination: { offset?: number; limit?: number };
  data?: SearchMediaResponse;
  queryVars?: SearchMediaQueryArgs;
}
export interface SearchSliceState {
  selectedResults: SelectedRow[];
  searchFiles: {
    searchParams?: FileSearchParams;
    groupedSearch: {
      [group in ResultCategory]?: SearchResults;
    };
    ungroupedSearch: SearchResults;
  };
  filterParams?: FilterFormValue;
  entitySearch: {
    status: ApiStatus;
    data: AutocompleteResponse['fields']['entityName'];
    error?: string;
  };
  librarySearch: {
    status: ApiStatus;
    data: AutocompleteResponse['fields']['libraryName'];
    error?: string;
  };
  objectSearch: {
    status: ApiStatus;
    data: AutocompleteResponse['fields']['object-recognition.series.found'];
    error?: string;
  };
  viewType: ViewType;
  sortCategory: SortCategory;
  searchView: SearchView;
  categories: Category[];
  hideIfNoResults: boolean;
  blur: boolean;
  sort: {
    type: FILES_SORT_FIELD;
    order: FILES_SORT_DIRECTION;
  };
  showAddToCaseDialog: boolean;
  showMoveToCaseDialog: boolean;
  moveFileStatus: ApiStatus;
  unfileFromCaseStatus: ApiStatus;
}

export const initialState: SearchSliceState = {
  selectedResults: [],
  searchFiles: {
    groupedSearch: {},
    searchParams: undefined,
    ungroupedSearch: {
      status: 'idle',
      pagination: {
        offset: 0,
        limit: 0,
      },
      data: undefined,
    },
  },
  entitySearch: {
    status: 'idle',
    data: undefined,
  },
  librarySearch: {
    status: 'idle',
    data: undefined,
  },
  objectSearch: {
    status: 'idle',
    data: [],
  },
  viewType: getViewTypeLocalStorage(),
  sortCategory: getSortCategoryLocalStorage(),
  searchView: getSearchViewLocalStorage(),
  categories: getCategoriesLocalStorage(),
  hideIfNoResults: getHideIfNoResultsLocalStorage(),
  blur: getBlurValue(),
  sort: {
    type: 'veritone-file.filename',
    order: 'asc',
  },
  showAddToCaseDialog: false,
  showMoveToCaseDialog: false,
  moveFileStatus: 'idle',
  unfileFromCaseStatus: 'idle',
};

const validateFaceCognition = (params: FileSearchParams) => {
  if (!params.cognitionFilters?.faceDetection?.terms) {
    return;
  }

  validateFaceCognitionTerms(params.cognitionFilters.faceDetection.terms);
};

export const validateFaceCognitionTerms = (terms: FaceDetectionTerm[]) => {
  terms.forEach((term) => {
    if (term.type === 'operator' && !term.operator) {
      throw new Error('type operator must have an operator defined');
    }
    if (term.type === 'entity' && !term.entityId) {
      throw new Error('type entity must have an entityId defined');
    }
    if (term.type === 'library' && !term.libraryId) {
      throw new Error('type library must have an libraryId defined');
    }
    if (term.type === 'string' && !term.stringQuery) {
      throw new Error('type string must have an stringQuery defined');
    }
    if (
      term.type === 'operator' &&
      term.operator &&
      !['and', 'or', '(', ')'].includes(term.operator)
    ) {
      throw new Error(
        'type operator must have an operator "and", "or", "(", ")"'
      );
    }
    if (
      term.type === 'operator' &&
      (term.entityId || term.libraryId || term.stringQuery)
    ) {
      throw new Error(
        'type operator must not have an entityId, libraryId, or stringQuery defined'
      );
    }
    if (
      term.type === 'entity' &&
      (term.operator || term.libraryId || term.stringQuery)
    ) {
      throw new Error(
        'type entity must not have an operator, libraryId, or stringQuery defined'
      );
    }
    if (
      term.type === 'library' &&
      (term.operator || term.entityId || term.stringQuery)
    ) {
      throw new Error(
        'type library must not have an operator, entityId, or stringQuery defined'
      );
    }
    if (
      term.type === 'string' &&
      (term.operator || term.entityId || term.libraryId)
    ) {
      throw new Error(
        'type string must not have an operator, entityId, or libraryId defined'
      );
    }
  });

  if (!terms) {
    return;
  }

  const validationResult = isParamTermValid(terms);
  if (!validationResult.isValid) {
    if (validationResult.reason === 'unmatched_parentheses') {
      throw new Error('Unmatched parentheses');
    } else if (validationResult.reason === 'no_operator_between_parentheses') {
      throw new Error('There must be at an operator between parentheses');
    } else if (validationResult.reason === 'operator_at_wrong_position') {
      throw new Error('The operator is at the wrong position');
    } else if (validationResult.reason === 'no_operator_found_at_position') {
      throw new Error(
        `An operator is expected at this position, but none was found`
      );
    }
  }
};

const validateObjectCognition = (params: QueryObjectSearchParams) => {
  if (!params.objectDetection?.terms) {
    return;
  }

  validateObjectCognitionTerms(params.objectDetection?.terms);
};

export const validateObjectCognitionTerms = (terms: ObjectDetectionTerm[]) => {
  terms.forEach((term) => {
    if (term.type === 'operator' && !term.operator) {
      throw new Error('type operator must have an operator defined');
    }
    if (term.type === 'foundFullTextString' && !term.foundFullTextString) {
      throw new Error(
        'type foundFullTextString must have an foundFullTextString defined'
      );
    }
    if (term.type === 'foundString' && !term.foundString) {
      throw new Error('type foundString must have an foundString defined');
    }
    if (
      term.type === 'operator' &&
      term.operator &&
      !['and', 'or', '(', ')'].includes(term.operator)
    ) {
      throw new Error(
        'type operator must have an operator "and", "or", "(", ")"'
      );
    }
    if (
      term.type === 'operator' &&
      (term.foundFullTextString || term.foundString)
    ) {
      throw new Error(
        'type operator must not have an foundFullTextString or foundString defined'
      );
    }
    if (
      term.type === 'foundFullTextString' &&
      (term.operator || term.foundString)
    ) {
      throw new Error(
        'type foundFullTextString must not have an operator or foundString defined'
      );
    }
    if (
      term.type === 'foundString' &&
      (term.operator || term.foundFullTextString)
    ) {
      throw new Error(
        'type foundString must not have an operator or foundFullTextString defined'
      );
    }
  });

  if (!terms) {
    return;
  }
  const validationResult = isParamTermValid(terms);
  if (!validationResult.isValid) {
    if (validationResult.reason === 'unmatched_parentheses') {
      throw new Error('Unmatched parentheses');
    } else if (validationResult.reason === 'no_operator_between_parentheses') {
      throw new Error('There must be at an operator between parentheses');
    } else if (validationResult.reason === 'operator_at_wrong_position') {
      throw new Error('The operator is at the wrong position');
    } else if (validationResult.reason === 'no_operator_found_at_position') {
      throw new Error(
        `An operator is expected at this position, but none was found`
      );
    }
  }
};

export function isParamTermValid(
  input: (FaceDetectionTerm | ObjectDetectionTerm)[]
): { isValid: boolean; reason?: ParamTermInvalidReason } {
  if (isEmpty(input)) {
    return { isValid: true };
  }
  // add open and close parentheses to the input for validation
  const terms = [
    { type: 'operator', operator: MathOperators.OPEN_PARENTHESIS },
    ...input,
    { type: 'operator', operator: MathOperators.CLOSE_PARENTHESIS },
  ];
  const stack = [] as number[];
  for (let i = 0; i < terms.length; i++) {
    const cur = terms[i];
    if (cur.type !== 'operator') {
      continue;
    }
    if (cur.operator === MathOperators.OPEN_PARENTHESIS) {
      stack.push(i);
    } else if (cur.operator === MathOperators.CLOSE_PARENTHESIS) {
      const openParenthesisPos = stack.pop();
      // if no open parenthesis, return false
      if (openParenthesisPos === undefined) {
        return {
          isValid: false,
          reason: 'unmatched_parentheses',
        };
      }
      const left = openParenthesisPos + 1;
      const right = i - 1;
      // if nothing between the parentheses, return false
      if (left > right) {
        return {
          isValid: false,
          reason: 'no_operator_between_parentheses',
        };
      }

      // if the segment is not in pattern of non-operator, operator, non-operator, return false
      for (let j = left; j <= right; j += 2) {
        if (terms[j].type === 'operator') {
          return {
            isValid: false,
            reason: 'operator_at_wrong_position',
          };
        }
        if (j < right && terms[j + 1].type !== 'operator') {
          return {
            isValid: false,
            reason: 'no_operator_found_at_position',
          };
        }
      }
      if (terms[right].type === 'operator') {
        return {
          isValid: false,
          reason: 'operator_at_wrong_position',
        };
      }

      // replace the segment with a placeholder for next iteration
      terms.splice(openParenthesisPos, i - openParenthesisPos + 1, {
        type: 'entity',
        entityId: 'placeholder',
      });
      i = openParenthesisPos;
    }
  }
  if (stack.length !== 0) {
    return {
      isValid: false,
      reason: 'unmatched_parentheses',
    };
  }
  return { isValid: true };
}

export const searchSlice = createAppSlice({
  name: 'search',
  initialState: {
    ...initialState,
    formValues: {
      search: '',
      metadata: '',
      caseStatus: '',
      caseId: '',
      uploadDate: {
        startDate: '',
        endDate: '',
      },
      retentionDate: '',
      fileStatus: '',
      filename: '',
      wordsInTranscription: '',
      faceDetections: [] as CognitionItem[],
      objectDescriptors: [] as CognitionItem[],
      vehicleRecognition: '',
      licensePlateRecognition: '',
      textRecognition: '',
      callRecording: false,
      arrestReport: false,
      bodyWornCamera: false,
      bookingPhoto: false,
      citizenSubmittedVideo: false,
      crimeScenePhoto: false,
      inCarVideo: false,
      interviewAudioRecording: false,
      interviewRoomRecording: false,
      mobileDeviceExtraction: false,
      securityCameraVideo: false,
      photo: false,
      video: false,
      audio: false,
      document: false,
      image: false,
    } as FilterFormValue,
  },
  reducers: (create) => {
    const createThunk = create.asyncThunk.withTypes<{
      getState: () => RootState;
      extra: { http: HttpClient };
    }>();

    return {
      updateSelectedResults: create.reducer(
        (state, action: PayloadAction<SelectedRow[]>) => {
          state.selectedResults = action.payload;
        }
      ),
      setFileSearchResult: createThunk(
        async (
          payload: {
            params?: FileSearchParams;
            category?: ResultCategory;
            pagination?: Partial<
              Record<
                // TODO: ResultCategory is just string, should it be more specific?
                // ResultCategory | 'ungrouped',
                ResultCategory,
                { offset?: number; limit?: number }
              >
            >;
            isGrouped?: boolean;
            status: 'idle' | 'loading' | 'success' | 'failure' | 'aborted';
            data?: SearchMediaResponse;
            queryVars?: SearchMediaQueryArgs;
          },
          thunkAPI
        ) => {
          const data = payload.data;

          if (!data) {
            return data;
          }

          const { getState, dispatch } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          const results = data.searchMedia.jsondata.results;

          const uniqueTreeObjectIds = [
            ...new Set(
              results
                .map((r) => r.recording.parentTreeObjectIds?.[0])
                .filter((id) => !!id)
            ),
          ];

          const folderDataArray = await Promise.all(
            uniqueTreeObjectIds.map(async (id) => {
              try {
                const folder = await gql.getFolder({ folderId: id });
                return [
                  id,
                  folder.contentTemplates?.[0]?.sdo.data.caseId,
                ] as const;
              } catch {
                return null;
              }
            })
          );
          const recordingIds = results.map(
            (media) => media.recording.recordingId
          );
          const uniqueRecordingIds = [...new Set(recordingIds)];
          // TODO: Replace batch query with a multiple query simple descriptions
          const queriesMetadata = async () => {
            try {
              const metadataList =
                await gql.getFileMetadata(uniqueRecordingIds);
              const results = metadataList.temporalDataObjects.records.map(
                (recordMetadata) => {
                  const recordingId = recordMetadata.id;
                  const { createdBy, createdByName } = recordMetadata;
                  if (recordMetadata && createdBy) {
                    const user = getOwner({
                      data: { createdBy, createdByName },
                      gql,
                      dispatch,
                    });
                    return [recordingId, user];
                  }
                  return [recordingId, ''];
                }
              );

              const resolvedResults = await Promise.all(
                results.map(async ([id, user]) => [id, await user])
              );
              return resolvedResults;
            } catch {
              return uniqueRecordingIds.map((recordingId) => [recordingId, '']);
            }
          };
          const getCreators = await queriesMetadata();

          const descriptionMapper = await getDescriptions(recordingIds, gql);

          const caseIdMapper = fromEntries(
            folderDataArray.filter((item) => item !== null)
          );
          // TODO: Replace with fromEntries and fix types
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
          const creatorMapper: Record<string, string | null> =
            Object.fromEntries(getCreators.filter((item) => item !== null));

          const updatedResults = results.map((result) => {
            const folderId = result.recording?.parentTreeObjectIds?.[0];
            const recordingId = result.recording.recordingId;
            const description = descriptionMapper[recordingId];
            const creator = creatorMapper[recordingId];

            if (folderId) {
              result.recording.caseId = caseIdMapper[folderId];
            }

            if (description) {
              result.recording.description = description;
            }
            if (creator) {
              result.recording.creator = creator;
            }

            return result;
          });
          return {
            ...data,
            searchMedia: {
              ...data.searchMedia,
              jsondata: {
                ...data.searchMedia.jsondata,
                results: updatedResults,
              },
            },
          };
        },
        {
          fulfilled: (state, action) => {
            const {
              category,
              status,
              isGrouped,
              pagination,
              params,
              queryVars,
            } = action.meta.arg;

            const data = action.payload;

            if (params) {
              state.searchFiles.searchParams = params;
            }

            //  Search is aborted.  Reset the state to default
            // if (status === 'aborted') {
            //   state.searchFiles.ungroupedSearch.status = 'failure';
            //   state.searchFiles.searchParams = undefined;
            //   state.searchFiles.groupedSearch = {};
            //   return;
            // }

            if (isGrouped && !!category) {
              if (!state.searchFiles.groupedSearch[category]) {
                state.searchFiles.groupedSearch[category] = {
                  status: 'idle',
                  pagination: { offset: 0, limit: 0 },
                  data: undefined,
                };
              }

              if (status) {
                state.searchFiles.groupedSearch[category].status = status;
              }
              if (data) {
                state.searchFiles.groupedSearch[category].data = data;
              }
              if (pagination && pagination[category]) {
                state.searchFiles.groupedSearch[category].pagination =
                  pagination[category];
              }
              if (queryVars) {
                state.searchFiles.groupedSearch[category].queryVars = queryVars;
              }
            }

            if (!isGrouped) {
              if (status) {
                state.searchFiles.ungroupedSearch.status = status;
              }
              if (pagination?.ungrouped) {
                state.searchFiles.ungroupedSearch.pagination =
                  pagination.ungrouped;
              }
              if (data) {
                state.searchFiles.ungroupedSearch.data = data;
              }
              if (queryVars) {
                state.searchFiles.ungroupedSearch.queryVars = queryVars;
              }
            }
          },
          rejected: (state, action) => {
            // Mark the category as failed
            const { category, isGrouped } = action.meta.arg;
            if (
              category &&
              isGrouped &&
              state.searchFiles.groupedSearch[category]
            ) {
              state.searchFiles.groupedSearch[category].status = 'failure';
            } else if (!isGrouped) {
              state.searchFiles.ungroupedSearch.status = 'failure';
            }
          },
        }
      ),
      searchFiles: createThunk(
        async (params: FileSearchParams, thunkAPI) => {
          const { getState, dispatch, signal } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);

          validateFaceCognition(params);
          validateObjectCognition(params);

          await searchFilesFn({
            params,
            lastParams: state.search.searchFiles.searchParams,
            gql,
            config,
            token,
            dispatch,
            abortSignal: signal,
          });
        },
        {
          rejected: (state, action) => {
            const { pagination, searchResultType, checkedResultCategories } =
              action.meta.arg;

            if (action.error.name === 'AbortError') {
              state.searchFiles.ungroupedSearch.status = 'idle';
              state.searchFiles.searchParams = undefined;
              state.searchFiles.groupedSearch = {};
              return;
            }

            state.searchFiles.ungroupedSearch.status = 'failure';
            state.searchFiles.searchParams = {
              pagination,
              searchResultType,
              checkedResultCategories,
            };

            //  Show error message with the GQL request ID
            //  Split on ':' and take the last part - the request ID
            const GQLRequestId = action.error.message?.split(':').pop() || '';
            const searchRejectMessage = I18nTranslate.TranslateMessage(
              'somethingWrongSearch',
              {
                gqlRequestId:
                  GQLRequestId?.length > 0 ? ` (${GQLRequestId})` : '',
              }
            );
            enqueueSnackbar(searchRejectMessage, { variant: 'error' });
          },
        }
      ),
      getEntitySearch: createThunk(
        async (keyword: string, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const data = await queryEntitySearch({
            keyword,
            gql,
            config,
          });
          return data?.data.fields.entityName;
        },
        {
          pending: (state) => {
            state.entitySearch.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.entitySearch.status = 'complete';
            state.entitySearch.data = action.payload;
          },
          rejected: (state) => {
            state.entitySearch.status = 'failure';
            state.entitySearch.data = [];
          },
        }
      ),
      getLibrarySearch: createThunk(
        async (keyword: string, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const data = await queryLibrarySearch({
            keyword,
            gql,
            config,
          });
          return data?.data.fields.libraryName;
        },
        {
          pending: (state) => {
            state.librarySearch.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.librarySearch.status = 'complete';
            state.librarySearch.data = action.payload;
          },
          rejected: (state) => {
            state.librarySearch.status = 'failure';
            state.librarySearch.data = [];
          },
        }
      ),
      getObjectSearch: createThunk(
        async (params: FileSearchParams, thunkAPI) => {
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);

          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const data = await queryObjectSearch({
            params,
            gql,
            config,
          });
          return data.data.fields['object-recognition.series.found'];
        },
        {
          pending: (state) => {
            state.objectSearch.status = 'loading';
          },
          fulfilled: (state, action) => {
            state.objectSearch.status = 'complete';
            state.objectSearch.data = action.payload;
          },
          rejected: (state, action) => {
            state.objectSearch.status = 'failure';
            state.objectSearch.error = action.error.message;
          },
        }
      ),
      addFilesToCase: createThunk(
        async (payload: { caseId: string; fileIds: string[] }, thunkAPI) => {
          const { caseId, fileIds } = payload;
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const promises = fileIds.map(async (fileId) => {
            const tdoFolder = await gql.getTDOFolder({
              tdoId: fileId,
            });
            if (tdoFolder.length > 0 && tdoFolder[0].id) {
              return gql.moveFile({
                fileId,
                oldFolderId: tdoFolder[0].id,
                newFolderId: caseId,
              });
            } else {
              return gql.fileTemporalDataObject({
                tdoId: fileId,
                folderId: caseId,
              });
            }
          });
          await Promise.all(promises);
        },
        {
          pending: (state) => {
            state.moveFileStatus = 'loading';
          },
          fulfilled: (state) => {
            state.moveFileStatus = 'complete';
            state.showAddToCaseDialog = false;
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseUpdatedSuccess'),
              {
                variant: 'success',
              }
            );
          },
          rejected: (state) => {
            state.showAddToCaseDialog = false;
            state.moveFileStatus = 'failure';
            enqueueSnackbar(
              I18nTranslate.TranslateMessage('caseUpdateFailed'),
              {
                variant: 'error',
              }
            );
          },
        }
      ),
      unfileFromCase: createThunk(
        async (payload: { tdoId: string }, thunkAPI) => {
          const { tdoId } = payload;
          const { getState } = thunkAPI;
          const state = getState() as RootState;
          const token = getApiAuthToken(state);
          if (!token) {
            throw new Error('no auth token');
          }
          const config = state.config;
          const gqlEndpoint = `${config.apiRoot}/${config.graphQLEndpoint}`;
          const gql = new GQLApi(gqlEndpoint, token, config.veritoneAppId);
          const tdoFolder = await gql.getTDOFolder({
            tdoId,
          });
          if (tdoFolder.length > 0 && tdoFolder[0].id) {
            await gql.unfileTemporalDataObject({
              tdoId,
              folderId: tdoFolder[0].id,
            });
          }
        },
        {
          pending: (state) => {
            state.unfileFromCaseStatus = 'loading';
          },
          fulfilled: (state) => {
            state.unfileFromCaseStatus = 'complete';
          },
          rejected: (state) => {
            state.unfileFromCaseStatus = 'failure';
          },
        }
      ),
      setFormValues: create.reducer(
        (state, action: PayloadAction<FilterFormValue>) => {
          state.formValues = action.payload;
        }
      ),
      toggleViewType: create.reducer((state) => {
        const currViewType = state.viewType;
        const viewType =
          currViewType === ViewType.LIST ? ViewType.GRID : ViewType.LIST;

        state.viewType = viewType;
        setViewTypeLocalStorage(viewType);
      }),
      changeSortCategory: create.reducer(
        (state, action: PayloadAction<string>) => {
          const sortCategory = action.payload;
          if (isSortCategory(sortCategory)) {
            state.sortCategory = sortCategory;
            setSortCategoryLocalStorage(sortCategory);
          }
        }
      ),
      changeSearchView: create.reducer(
        (state, action: PayloadAction<string>) => {
          const view = action.payload;
          if (isSearchView(view)) {
            state.searchView = view;
            setSearchViewLocalStorage(view);
          }
        }
      ),
      sortCategories: create.reducer(
        (state, action: PayloadAction<Category[]>) => {
          const categories = action.payload;
          if (isEqualCategories(categories)) {
            state.categories = categories;
            setCategoriesLocalStorage(categories);
          }
        }
      ),
      toggleHideIfNoResults: create.reducer((state) => {
        const hideIfNoResults = state.hideIfNoResults;
        state.hideIfNoResults = !hideIfNoResults;
        setHideIfNoResultsLocalStorage(!hideIfNoResults);
      }),
      expandCategory: create.reducer(
        (
          state,
          action: PayloadAction<{ category: ResultCategory; expanded: boolean }>
        ) => {
          const { category, expanded } = action.payload;
          state.categories = state.categories.map((c) => {
            if (c.category === category) {
              c.expanded = expanded;
            }
            return c;
          });
          setCategoriesLocalStorage(state.categories);
        }
      ),
      expandAllCategories: create.reducer(
        (state, action: PayloadAction<boolean>) => {
          state.categories = state.categories.map((c) => {
            c.expanded = action.payload;
            return c;
          });
          setCategoriesLocalStorage(state.categories);
        }
      ),
      setFilterParams: create.reducer(
        (state, action: PayloadAction<FilterFormValue>) => {
          state.filterParams = action.payload;
        }
      ),
      resetFilterParams: create.reducer((state) => {
        state.filterParams = undefined;
      }),
      toggleBlur: create.reducer((state) => {
        const newBlur = !state.blur;
        state.blur = newBlur;
        setBlurValueLocalStorage(newBlur);
      }),
      resetSearchToDefault: create.reducer((state) => {
        state.searchFiles = {
          groupedSearch: {},
          searchParams: undefined,
          ungroupedSearch: {
            status: 'idle',
            pagination: {
              offset: 0,
              limit: 0,
            },
            data: undefined,
          },
        };
        state.entitySearch = {
          status: 'idle',
          data: undefined,
        };
        state.librarySearch = {
          status: 'idle',
          data: undefined,
        };
        state.objectSearch = {
          status: 'idle',
          data: [],
        };
        state.selectedResults = [];
      }),
      resetEntityAndLibrarySearch: create.reducer((state) => {
        state.entitySearch = initialState.entitySearch;
        state.librarySearch = initialState.librarySearch;
      }),
      resetObjectSearch: create.reducer((state) => {
        state.objectSearch = initialState.objectSearch;
      }),
      setSortBy: create.reducer(
        (state, action: PayloadAction<FILES_SORT_FIELD>) => {
          state.sort.type = action.payload;
        }
      ),
      setSortDirection: create.reducer(
        (state, action: PayloadAction<FILES_SORT_DIRECTION>) => {
          state.sort.order = action.payload;
        }
      ),
      setShowAddToCaseDialog: create.reducer(
        (state, action: PayloadAction<boolean>) => {
          state.showAddToCaseDialog = action.payload;
        }
      ),
      setShowMoveToCaseDialog: create.reducer(
        (state, action: PayloadAction<boolean>) => {
          state.showMoveToCaseDialog = action.payload;
        }
      ),
      updateFile: create.reducer(
        (
          state,
          action: PayloadAction<{
            tdoId: string;
            fileName: string;
          }>
        ) => {
          const { tdoId, fileName } = action.payload;
          const data = state.searchFiles.ungroupedSearch.data;
          if (data) {
            for (const result of data.searchMedia.jsondata.results) {
              if (result.recording.recordingId === tdoId) {
                for (const context of result.context) {
                  if (!context['veritone-file']) {
                    context['veritone-file'] = {};
                  }
                  context['veritone-file'].filename = fileName;
                }
              }
            }
          }
        }
      ),
    };
  },
  selectors: {
    selectSelectedResults: (state) => state.selectedResults,
    selectViewType: (state) => state.viewType,
    selectSortCategory: (state) => state.sortCategory,
    selectSearchView: (state) => state.searchView,
    selectCategories: (state) => state.categories,
    selectHideIfNoResults: (state) => state.hideIfNoResults,
    selectSearchFiles: (state) => state.searchFiles,
    selectEntitySearch: (state) => state.entitySearch,
    selectLibrarySearch: (state) => state.librarySearch,
    selectObjectSearch: (state) => state.objectSearch,
    selectBlur: (state) => state.blur,
    selectSort: (state) => state.sort,
    selectShowAddToCaseDialog: (state) => state.showAddToCaseDialog,
    selectShowMoveToCaseDialog: (state) => state.showMoveToCaseDialog,
    selectFormValues: (state) => state.formValues,
    selectFilterParams: (state) => state.filterParams,
  },
});

export const {
  updateSelectedResults,
  setFileSearchResult,
  searchFiles,
  toggleViewType,
  changeSortCategory,
  changeSearchView,
  sortCategories,
  toggleHideIfNoResults,
  getEntitySearch,
  getLibrarySearch,
  getObjectSearch,
  expandCategory,
  expandAllCategories,
  toggleBlur,
  resetSearchToDefault,
  resetEntityAndLibrarySearch,
  resetObjectSearch,
  setSortBy,
  setSortDirection,
  setShowAddToCaseDialog,
  setShowMoveToCaseDialog,
  addFilesToCase,
  unfileFromCase,
  updateFile,
  setFormValues,
} = searchSlice.actions;

export const {
  selectSelectedResults,
  selectViewType,
  selectSortCategory,
  selectSearchView,
  selectCategories,
  selectHideIfNoResults,
  selectSearchFiles,
  selectEntitySearch,
  selectLibrarySearch,
  selectObjectSearch,
  selectBlur,
  selectSort,
  selectShowAddToCaseDialog,
  selectShowMoveToCaseDialog,
  selectFormValues,
  selectFilterParams,
} = searchSlice.selectors;
