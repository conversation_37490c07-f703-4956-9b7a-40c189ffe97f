import './index.scss';
import {
  Dashboard,
  FolderClosed,
  FolderOpen,
  Search,
  Settings,
  Share,
} from '@assets/icons';
import Menu from '@components/Menu';
import { I18nTranslate } from '@i18n';
import {
  Add as AddIcon,
  BedtimeOutlined as BedtimeOutlinedIcon,
  DescriptionOutlined as DescriptionOutlinedIcon,
  LightMode as LightModeIcon,
  WorkOutlineOutlined as WorkOutlineOutlinedIcon,
} from '@mui/icons-material';
import { Box, Button, MenuItem, Tooltip } from '@mui/material';
import {
  selectCaseData,
  toggleCaseDrawer,
} from '@store/modules/caseManager/slice';
import { selectConfig } from '@store/modules/config/slice';
import { switchCurrentTheme, useToggleTheme } from '@theme';
import cn from 'classnames';
import { useSnackbar } from 'notistack';
import { MouseEvent, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router';

const navList = [
  {
    id: 'dashboard',
    label: 'dashboard',
    testLabel: 'dashboard',
    iconOpen: <Dashboard />,
    iconClosed: <Dashboard />,
    path: '/dashboard',
    disabled: true,
  },
  {
    id: 'search',
    label: 'search',
    testLabel: 'search',
    iconOpen: <Search />,
    iconClosed: <Search />,
    path: '/search',
  },
  {
    id: 'case-manager',
    label: 'caseManager',
    testLabel: 'case-manager',
    iconOpen: <FolderOpen />,
    iconClosed: <FolderClosed />,
    path: '/case-manager',
  },
  {
    id: 'share',
    label: 'share',
    testLabel: 'share',
    iconOpen: <Share />,
    iconClosed: <Share />,
    path: '/share',
    disabled: true,
  },
  {
    id: 'settings',
    label: 'settings',
    testLabel: 'settings',
    iconOpen: <Settings />,
    iconClosed: <Settings />,
    path: '/settings',
  },
];

const NavBar = () => {
  const intl = I18nTranslate.Intl();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { pathname } = useLocation();
  const toggleTheme = useToggleTheme();
  const { enqueueSnackbar } = useSnackbar();
  const selectedCase = useSelector(selectCaseData);
  const { registryIds } = useSelector(selectConfig);

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleAddMenu = (event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => setAnchorEl(null);

  const handleCreateNewCase = () => {
    dispatch(toggleCaseDrawer());
    handleClose();
  };

  const handleUploadFile = () => {
    if (window.aiware) {
      window.aiware.mountPanel({
        panelId: 'DATA_CENTER_IMPORTER',
        microFrontend: {
          name: 'DATA_CENTER_IMPORTER',
          config: {
            title: '',
            fileLimit: 10,
            titleSubText: '',
            name: intl.formatMessage({ id: 'fileUploader' }),
            panelTitle: intl.formatMessage({ id: 'uploadFiles' }),
            locationFolderTitle: intl.formatMessage({ id: 'chooseACase' }),
            locationFolderSubText: intl.formatMessage({
              id: 'selectStoredLocation',
            }),
            locationFolderInputLabel: intl.formatMessage({ id: 'case' }),
            hidePermissionsManagement: true,
            hideNewFolderButton: true,
            registryId: registryIds?.caseRegistryId,
            activeFolder: {
              id: selectedCase.folderId,
              name: selectedCase.caseName,
            },
          },
        },
        panelConfig: {
          dimmed: 0,
          zIndex: 1000,
          marginTop: 0,
          size: 'large',
          marginStart: 0,
          type: 'APP_BAR_PANEL_TEMPLATE',
        },
      });
    } else {
      enqueueSnackbar(
        <div>
          <strong>{intl.formatMessage({ id: 'unableToUploadFile' })}</strong>
          {/* TODO: Create custom snackbar for consistency */}
          <div className="snackbar-subtitle">
            {intl.formatMessage({ id: 'refreshPage' })}
          </div>
        </div>,
        {
          variant: 'error',
        }
      );
    }
    handleClose();
  };

  // const handleCreateNewShare = () => {
  //   handleClose();
  // };

  return (
    <>
      <Box
        gap={1}
        className="tab-panel"
        flexDirection="column"
        sx={{ display: 'flex' }}
      >
        <Tooltip
          placement="right"
          title={I18nTranslate.TranslateMessage('add')}
        >
          <Button className="add-button" onClick={handleAddMenu}>
            <AddIcon />
          </Button>
        </Tooltip>
        {navList
          .filter(({ disabled }) => !disabled)
          .map((tab) => {
            const selected = pathname.includes(tab.path);
            return (
              <Tooltip
                key={tab.id}
                placement="right"
                data-test={`nav-tab-${tab.testLabel}`}
                title={intl.formatMessage({ id: tab.label })}
              >
                <Button
                  onClick={() => {
                    if (!selected) {
                      navigate(tab.path);
                    }
                    return;
                  }}
                  className={cn('tab-button', { selected })}
                >
                  {selected ? tab.iconOpen : tab.iconClosed}
                </Button>
              </Tooltip>
            );
          })}
        <Tooltip
          placement="right"
          title={
            toggleTheme.isDark
              ? intl.formatMessage({ id: 'lightMode' })
              : intl.formatMessage({ id: 'darkMode' })
          }
        >
          <Button
            className="tab-button"
            style={{ marginTop: 'auto' }}
            onClick={switchCurrentTheme(toggleTheme)}
          >
            {toggleTheme.isDark ? <LightModeIcon /> : <BedtimeOutlinedIcon />}
          </Button>
        </Tooltip>
      </Box>
      <Menu
        id="nav-bar-menu"
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <MenuItem
          data-testid="create-new-case-btn"
          onClick={handleCreateNewCase}
          disableRipple
        >
          <WorkOutlineOutlinedIcon />
          {I18nTranslate.TranslateMessage('createNewCase')}
        </MenuItem>
        <MenuItem
          data-testid="upload-files-btn"
          onClick={handleUploadFile}
          disableRipple
        >
          <DescriptionOutlinedIcon />
          {I18nTranslate.TranslateMessage('uploadFile(s)')}
        </MenuItem>
        {/* <MenuItem onClick={handleCreateNewShare} disableRipple> */}
        {/*  <ShareOutlinedIcon /> */}
        {/*  {I18nTranslate.TranslateMessage('createNewShare')} */}
        {/* </MenuItem> */}
      </Menu>
    </>
  );
};

export default NavBar;
