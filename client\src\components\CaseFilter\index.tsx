import './index.scss';
import { I18nTranslate } from '@i18n';
import CloseIcon from '@mui/icons-material/Close';
import { Button } from '@mui/material';
import FormControl from '@mui/material/FormControl';
import IconButton from '@mui/material/IconButton';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import TextField from '@mui/material/TextField';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  selectFetchedStatuses,
  selectFetchedTags,
  selectUpdatedCaseStatuses,
} from '@store/modules/settings/slice.ts';
import { useSelector } from 'react-redux';

import { CaseFilterValue } from '@shared-types/types';
import { voidWrapper } from '@utils/helpers';
import { Controller, useForm } from 'react-hook-form';

interface Props {
  filterValue: CaseFilterValue;
  onClose: () => void;
  onApply: (value: CaseFilterValue) => void;
}

const isDefaultValue = (
  filterValue: CaseFilterValue,
  isDirty?: boolean
): boolean =>
  isDirty
    ? false
    : !filterValue.caseName &&
      !filterValue.caseId &&
      !filterValue.statusId &&
      !filterValue.tagIds?.length;

function CaseFilter({ filterValue, onClose, onApply }: Props) {
  const intl = I18nTranslate.Intl();
  const statuses = useSelector(selectFetchedStatuses);
  const tags = useSelector(selectFetchedTags);
  const defaultCaseStatuses = useSelector(selectUpdatedCaseStatuses) || [];
  const updatedCaseStatuses = [...defaultCaseStatuses, ...statuses];
  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty, errors },
  } = useForm<CaseFilterValue>({
    defaultValues: {
      caseName: filterValue.caseName ?? '',
      caseId: filterValue.caseId ?? '',
      statusId: filterValue.statusId ?? '',
      tagIds: filterValue.tagIds ?? [],
    },
  });

  const handleCancel = () => {
    onClose();
  };
  const handleReset = () => {
    reset(
      {
        caseName: '',
        caseId: '',
        statusId: '',
        tagIds: [],
      },
      { keepDefaultValues: true }
    );
  };

  const disableSubmit =
    !!errors.caseName ||
    !!errors.caseId ||
    !isDirty ||
    isDefaultValue(filterValue, isDirty);
  const enableReset = !isDefaultValue(filterValue, isDirty);

  return (
    <div className="case-filter" data-testid="case-filter">
      <LocalizationProvider>
        <div className="case-filter__header-container">
          <div
            className="case-filter__header-title"
            data-testid="case-filter-title"
          >
            {I18nTranslate.TranslateMessage('filters')}
          </div>
          <div className="case-filter__header-actions">
            {enableReset && (
              <Button
                onClick={handleReset}
                className="case-filter__reset-button"
                data-testid="filter-reset-button"
              >
                {I18nTranslate.TranslateMessage('reset')}
              </Button>
            )}
            <IconButton
              onClick={handleCancel}
              data-testid="filter-close-button"
            >
              <CloseIcon />
            </IconButton>
          </div>
        </div>
        <div className="case-filter__main-container">
          <div className="case-filter__case-name-container">
            <div className="case-filter__case-name-title">
              {I18nTranslate.TranslateMessage('caseName')}
            </div>
            <Controller
              name="caseName"
              control={control}
              rules={{
                pattern: {
                  value: /^[a-zA-Z0-9\s_-]+$/,
                  message: intl.formatMessage({ id: 'allowedLetters' }),
                },
              }}
              render={({ field, fieldState }) => (
                <TextField
                  {...field}
                  error={!!fieldState.error}
                  helperText={fieldState.error?.message}
                  fullWidth
                  size="small"
                  className="case-filter__caseName-field"
                  slotProps={{
                    htmlInput: {
                      maxLength: 50,
                      'data-testid': 'case-filter-caseName-textfield',
                    },
                  }}
                />
              )}
            />
          </div>
          <div className="case-filter__case-id-container">
            <div className="case-filter-case-id-title">
              {I18nTranslate.TranslateMessage('caseID')}
            </div>
            <Controller
              name="caseId"
              control={control}
              rules={{
                pattern: {
                  value: /^[a-zA-Z0-9\s_-]+$/,
                  message: intl.formatMessage({ id: 'allowedLetters' }),
                },
              }}
              render={({ field, fieldState }) => (
                <TextField
                  {...field}
                  error={!!fieldState.error}
                  helperText={fieldState.error?.message}
                  fullWidth
                  size="small"
                  className="case-filter__caseId-field"
                  slotProps={{
                    htmlInput: {
                      maxLength: 25,
                      'data-testid': 'case-filter-caseId-textfield',
                    },
                  }}
                />
              )}
            />
          </div>

          <div className="case-filter__tags-container">
            <div className="case-filter__tags-title">
              {I18nTranslate.TranslateMessage('tags')}
            </div>
            <FormControl className="case-filter__tags-form-field">
              <Controller
                name="tagIds"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    displayEmpty
                    multiple
                    size="small"
                    data-testid="case-tags-filter"
                  >
                    {tags
                      .filter((tag) => tag.active)
                      .map((tag) => (
                        <MenuItem
                          key={tag.id}
                          value={tag.id}
                          data-testid={`case-tag-option-${tag.id}`}
                        >
                          {tag.label}
                        </MenuItem>
                      ))}
                  </Select>
                )}
              />
            </FormControl>
          </div>
          <div className="case-filter__status-container">
            <div className="case-filter__status-title">
              {I18nTranslate.TranslateMessage('status')}
            </div>
            <FormControl className="case-filter__status-form-field">
              <Controller
                name="statusId"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    displayEmpty
                    size="small"
                    data-testid="case-status-filter-select"
                  >
                    <MenuItem value="" data-testid="case-status-option-all">
                      {I18nTranslate.TranslateMessage('showAll')}
                    </MenuItem>
                    {updatedCaseStatuses
                      .filter((status) => status.active)
                      .map((status) => (
                        <MenuItem
                          key={status.id}
                          value={status.id}
                          data-testid={`case-status-option-${status.id}`}
                        >
                          {status.label}
                        </MenuItem>
                      ))}
                  </Select>
                )}
              />
            </FormControl>
          </div>
        </div>
        <div className="case-filter__footer-container">
          <Button
            onClick={handleCancel}
            color="inherit"
            className="case-filter__cancel-button"
            data-testid="case-filter-cancel-button"
          >
            {I18nTranslate.TranslateMessage('cancel')}
          </Button>
          <Button
            variant="contained"
            onClick={voidWrapper(handleSubmit(onApply))}
            disabled={disableSubmit}
            className="case-filter__apply-button"
            data-testid="case-filter-apply-button"
          >
            {I18nTranslate.TranslateMessage('apply')}
          </Button>
        </div>
      </LocalizationProvider>
    </div>
  );
}

export default CaseFilter;
