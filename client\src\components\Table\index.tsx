import './index.scss';
import Menu from '@components/Menu';
import SortLabel from '@components/Table/SortLabel';
import { useObserveScrollBar } from '@hooks';
import {
  FilterList as FilterListIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import {
  Badge,
  Box,
  CircularProgress,
  Divider,
  IconButton,
  IconButtonProps,
  MenuItem,
  Table as MuiTable,
  Paper,
  PopoverPosition,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
} from '@mui/material';
import { SortOrder } from '@store/modules/search/slice';
import { useVirtualizer } from '@tanstack/react-virtual';
import cn from 'classnames';
import { isFunction } from 'lodash';
import { enqueueSnackbar } from 'notistack';
import {
  ChangeEvent,
  MouseEvent,
  ReactElement,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useParams } from 'react-router';

export interface Column<T> {
  field?: keyof T;
  header: ReactNode;
  render?: ({
    value,
    rowId,
  }: {
    value?: T[keyof T];
    rowId?: string;
  }) => ReactNode;
  action?: () => void;
  isSortable?: boolean;
  width?: string;
}

export interface Action {
  action: string | ((rowId: string) => string);
  icon: ReactElement | ((rowId: string) => ReactElement);
  onClick: (rowId: string) => void;
  isDivider?: boolean;
}

export type DataMap<T> = Record<string, { index: number; item: T }>;

interface Props<T> {
  data: T[];
  dataMap: DataMap<T>;
  pendingDataCreate?: T[];
  columns: Column<T>[];
  row: {
    selected: string;
    handleSelect: (id: string) => void;
    handleDoubleClick: (rowId: string) => void;
    handleCtrlRowClick?: (id: string) => void;
    handleShiftRowClick?: (id: string) => void;
    pendingDeleteIds?: string[];
    pendingMoveFileIds?: {
      fileId: string;
      newFolderId: string;
      oldFolderId: string;
    }[];
  };
  sort: {
    orderBy: string;
    direction: SortOrder;
    handleSort: (field: string) => void;
  };
  pagination: {
    page: number;
    count: number;
    rowsPerPage: number;
    rowsPerPageOptions: number[];
    onPageChange: (
      event: MouseEvent<HTMLButtonElement> | null,
      newPage: number
    ) => void;
    onRowsPerPageChange: (event: ChangeEvent<HTMLInputElement>) => void;
  };
  styles: {
    classname?: string;
    isLoading?: boolean;
    emptyState?: ReactElement;
    noneCopyCell?: boolean;
    isFixedTableLayout?: boolean;
    fixedTableMinWidth?: string;
  };
  actions?: Action[];
  extraProps: {
    dispatchCurrentFile?: (fileId: string) => void; // dispatch current file for file moving
    pendingDeleteMessage?: string;
  };
  isLoadingCreateCase?: boolean;
  showHeader?: boolean;
  onFilter?: () => void;
  filterCount?: number;
  datatestId?: string;
}

const generateKey = () => Math.floor(Math.random() * 100000);

const Table = <T extends { id: string }>({
  data,
  dataMap,
  pendingDataCreate,
  columns,
  row: {
    selected,
    handleSelect,
    handleDoubleClick,
    handleCtrlRowClick,
    handleShiftRowClick,
    pendingDeleteIds,
    pendingMoveFileIds,
  },
  sort: { orderBy, direction, handleSort },
  pagination: {
    page,
    count,
    rowsPerPage,
    rowsPerPageOptions,
    onPageChange,
    onRowsPerPageChange,
  },
  styles: {
    classname,
    isLoading,
    emptyState,
    noneCopyCell,
    isFixedTableLayout,
    fixedTableMinWidth,
  },
  actions,
  extraProps: { dispatchCurrentFile, pendingDeleteMessage },
  showHeader = true,
  isLoadingCreateCase,
  onFilter,
  filterCount,
  datatestId: dataTestId,
}: Props<T>) => {
  const { selectedId: currentCaseId } = useParams();
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const rowRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const [menuAnchorEl, setMenuAnchorEl] = useState<null | HTMLElement>(null);
  const [selectRowMenu, setSelectRowMenu] = useState<string | null>(null);
  const [menuPosition, setMenuPosition] = useState<PopoverPosition | undefined>(
    undefined
  );
  const { hasScrollBar, containerRef } = useObserveScrollBar();

  const isActions = actions && actions.length;

  const allData = [...(pendingDataCreate ?? []), ...data];

  const hasLoading = isLoading || isLoadingCreateCase;

  useEffect(() => {
    if (!selected && data.length > 0 && tableContainerRef.current) {
      const activeElement = document.activeElement;
      if (activeElement !== tableContainerRef.current) {
        tableContainerRef.current.focus();
      }
    } else if (
      selected &&
      tableContainerRef.current &&
      rowRefs.current[selected]
    ) {
      rowRefs.current[selected].scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest',
      });
    }
  }, [selected, data.length]);

  const handleOpenMenu = (rowId: string, e: HTMLElement) => {
    setSelectRowMenu(rowId);
    if (e) {
      setMenuAnchorEl(e);
    }
    if (dispatchCurrentFile) {
      dispatchCurrentFile(rowId);
    }
  };

  const handleCloseMenu = () => {
    setMenuAnchorEl(null);
    setSelectRowMenu(null);
    setMenuPosition(undefined);
  };

  const handleTableRowContextMenu = (
    rowId: string,
    e: MouseEvent<HTMLDivElement | HTMLButtonElement>
  ) => {
    e.preventDefault();
    setMenuPosition({ top: e.clientY, left: e.clientX });
    handleOpenMenu(rowId, e.currentTarget);
  };

  const handleContextMenu: React.MouseEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    handleCloseMenu();
  };

  const handleClickRow = (rowId: string, e: MouseEvent<HTMLDivElement>) => {
    handleSelect(rowId);
    if (e.ctrlKey && handleCtrlRowClick) {
      handleCtrlRowClick(rowId);
    }
    if (e.shiftKey && handleShiftRowClick) {
      handleShiftRowClick(rowId);
    }
  };

  const checkRowPending = (rowId: string) => {
    const isPendingDelete = pendingDeleteIds?.includes(rowId);
    const isPendingCreate = pendingDataCreate?.some((r) => r.id === rowId);
    const isPendingMoveFile = pendingMoveFileIds?.some(
      (item) => item.oldFolderId === currentCaseId && item.fileId === rowId
    );

    return isPendingDelete || isPendingCreate || isPendingMoveFile;
  };

  const findNextValidIndex = (nextIndex: number, step: number) => {
    const dataLength = data.length;
    let index = nextIndex;

    while (index < dataLength && index >= 0) {
      const row = data[index];
      if (!checkRowPending(row.id)) {
        return index;
      }
      index = index + step;
    }

    return null;
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (data.length === 0) {
      return;
    }

    const navigationKeys = new Set([
      'Tab',
      'ArrowDown',
      'ArrowUp',
      'ArrowLeft',
      'ArrowRight',
    ]);

    if (navigationKeys.has(e.key)) {
      e.preventDefault();
    } else {
      return;
    }

    const currentIndex = !selected ? null : dataMap[selected].index;
    let nextItemIndex: number | null = null;

    switch (e.key) {
      case 'Tab':
        if (currentIndex === null) {
          nextItemIndex = findNextValidIndex(0, 1);
        }
        break;
      case 'ArrowDown':
      case 'ArrowRight':
        if (currentIndex !== null && currentIndex < data.length - 1) {
          nextItemIndex = findNextValidIndex(currentIndex + 1, 1);
        }
        break;
      case 'ArrowUp':
      case 'ArrowLeft':
        if (currentIndex !== null && currentIndex > 0) {
          nextItemIndex = findNextValidIndex(currentIndex - 1, -1);
        }
        break;
    }

    if (nextItemIndex !== null) {
      handleSelect(data[nextItemIndex].id);
    }
  };

  const filterWithBadge =
    filterCount && filterCount > 0 ? (
      <Badge
        color={'primary'}
        classes={{
          badge: 'filter-list-badge-background',
          colorPrimary: 'white',
        }}
        badgeContent={filterCount}
        variant={filterCount === 1 ? 'dot' : 'standard'}
      >
        <FilterListIcon
          className="table-header__filter-list-icon"
          data-testid="table-header__filter-list-icon"
        />
      </Badge>
    ) : (
      <FilterListIcon
        className="table-header__filter-list-icon"
        data-testid="table-header__filter-list-icon"
      />
    );

  const rowVirtualizer = useVirtualizer({
    count: allData.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => 69,
    overscan: 1,
  });

  return (
    <>
      <TableContainer
        elevation={0}
        component={Paper}
        className={classname}
        data-testid={dataTestId}
        ref={tableContainerRef}
        tabIndex={0}
        onKeyDown={handleKeyDown}
      >
        <Box className="table-content" sx={{ flex: 1, overflow: 'auto' }}>
          <Box
            className={cn('table-container', {
              'table-container-empty': isLoading,
              'table-container-fixed-layout': isFixedTableLayout,
            })}
            sx={{
              minWidth:
                isFixedTableLayout && fixedTableMinWidth
                  ? `${fixedTableMinWidth} !important`
                  : 'auto',
            }}
          >
            {showHeader && (
              <Box paddingRight={hasScrollBar ? '10px' : 0}>
                <MuiTable
                  className={cn({
                    'table-container-fixed-layout': isFixedTableLayout,
                  })}
                >
                  <TableHead className="table-header">
                    <TableRow>
                      {columns.map(({ field, header, isSortable, width }) => (
                        <SortLabel
                          direction={direction}
                          orderBy={orderBy}
                          field={String(field)}
                          header={header}
                          handleSort={handleSort}
                          isSortable={isSortable}
                          key={String(field)}
                          id={String(field)}
                          width={width}
                        />
                      ))}
                      {isActions && onFilter && (
                        <TableCell
                          sx={{ padding: '14px 20px 14px 0' }}
                          align="right"
                        >
                          <IconButton onClick={onFilter}>
                            {filterWithBadge}
                          </IconButton>
                        </TableCell>
                      )}
                      {isActions && !onFilter && <TableCell />}
                    </TableRow>
                  </TableHead>
                </MuiTable>
              </Box>
            )}
            <Box
              ref={containerRef}
              className="table-body"
              sx={{ height: '100%', overflow: 'auto' }}
            >
              <Box
                className={cn('table-body-wrapper', {
                  'table-container-fixed-layout': isFixedTableLayout,
                })}
                sx={{
                  width: '100%',
                  height: allData.length
                    ? `${rowVirtualizer.getTotalSize()}px`
                    : '100%',
                  position: 'relative',
                }}
              >
                {hasLoading ? (
                  <Box
                    display="flex"
                    justifyContent="center"
                    alignItems="center"
                    height="100%"
                    width="100%"
                  >
                    <CircularProgress data-testid="table-body_loading-icon" />
                  </Box>
                ) : allData.length ? (
                  rowVirtualizer.getVirtualItems().map((virtualRow) => {
                    const row = allData[virtualRow.index];
                    const isPendingDelete = pendingDeleteIds?.includes(row.id);

                    const isPending = checkRowPending(row.id);
                    return (
                      <Box
                        ref={(el: HTMLDivElement) => {
                          rowRefs.current[row.id] = el;
                        }}
                        key={`table-row-${generateKey()}-${row.id}`}
                        className={cn('body-row', {
                          selected: !isPending && selected === row.id,
                          'disable-row': isPending,
                        })}
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          transform: `translateY(${virtualRow.start}px)`,
                          height: 69,
                        }}
                        onClick={(e) => {
                          if (isPendingDelete && pendingDeleteMessage) {
                            enqueueSnackbar(pendingDeleteMessage, {
                              preventDuplicate: true,
                            });
                          } else {
                            handleClickRow(row.id, e);
                          }
                        }}
                        onDoubleClick={() => {
                          if (!isPending) {
                            handleDoubleClick(row.id);
                          }
                        }}
                        onContextMenu={(e) => {
                          if (!isPending) {
                            handleTableRowContextMenu(row.id, e);
                          }
                        }}
                        data-testid={`table-row-${row.id}`}
                      >
                        {columns.map(({ field, render, width }) => (
                          <Box
                            className="table-cell"
                            key={`table-cell-${generateKey()}-${row.id}-${String(field)}`}
                            sx={{
                              width,
                              cursor: 'default',
                              userSelect: noneCopyCell ? 'none' : 'auto',
                              whiteSpace: 'nowrap',
                              display: 'flex',
                              alignItems: 'center',
                            }}
                          >
                            {!field && render
                              ? render({ rowId: row.id })
                              : field && render
                                ? render({ value: row[field], rowId: row.id })
                                : field && !render
                                  ? String(row[field])
                                  : null}
                          </Box>
                        ))}
                        {isActions && (
                          <Box
                            className={cn('table-cell', 'menu-cell', {
                              'select-row-menu': selectRowMenu === row.id,
                              'select-row-menu-hidden': isPending,
                            })}
                          >
                            <IconButton
                              data-testid="search-table-menu"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleTableRowContextMenu(row.id, e);
                              }}
                              sx={{ visibility: 'visible' }}
                            >
                              <MoreVertIcon />
                            </IconButton>
                          </Box>
                        )}
                      </Box>
                    );
                  })
                ) : (
                  emptyState
                )}
              </Box>
            </Box>
          </Box>
        </Box>
        {data.length > 0 && (
          <TablePagination
            className="table-pagination"
            data-testid="table-pagination-container"
            rowsPerPageOptions={rowsPerPageOptions}
            component="div"
            sx={{ marginTop: 'auto' }}
            count={count}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={onPageChange}
            onRowsPerPageChange={onRowsPerPageChange}
            slotProps={{
              select: {
                inputProps: {
                  'data-testid': 'table-pagination-rows-per-page-select',
                },
              },
              actions: {
                nextButton: {
                  'data-testid': 'table-pagination-next-button',
                } as IconButtonProps,
                previousButton: {
                  'data-testid': 'table-pagination-previous-button',
                } as IconButtonProps,
              },
              displayedRows: {
                'data-testid': 'table-pagination-displayed-rows',
              } as React.HTMLAttributes<HTMLParagraphElement>,
            }}
          />
        )}
      </TableContainer>
      {isActions && selectRowMenu && (
        <Menu
          size="small"
          anchorEl={menuAnchorEl}
          open={Boolean(menuAnchorEl)}
          onClose={handleCloseMenu}
          anchorReference={menuPosition ? 'anchorPosition' : 'anchorEl'}
          anchorPosition={menuPosition}
          onContextMenu={handleContextMenu}
        >
          {actions.map(({ action, icon, onClick, isDivider }) => (
            <div key={isFunction(action) ? action(selectRowMenu) : action}>
              <MenuItem
                onClick={() => {
                  onClick(selectRowMenu);
                  handleCloseMenu();
                }}
                disableRipple
              >
                {isFunction(icon) ? icon(selectRowMenu) : icon}
                {isFunction(action) ? action(selectRowMenu) : action}
              </MenuItem>
              {isDivider && <Divider />}
            </div>
          ))}
        </Menu>
      )}
    </>
  );
};

export default Table;
