const HIDE_IF_NO_RESULTS_KEY = 'investigate-hide-if-no-results';

export const getHideIfNoResultsLocalStorage = () => {
  const storage = localStorage.getItem(HIDE_IF_NO_RESULTS_KEY);
  let hideIfNoResults = false;

  try {
    // TODO: Type of hideIfNoResults should be validated
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    hideIfNoResults = storage ? JSON.parse(storage) : false;
  } catch {
    hideIfNoResults = false;
  }

  return hideIfNoResults;
};

export const setHideIfNoResultsLocalStorage = (value: boolean) => {
  localStorage.setItem(HIDE_IF_NO_RESULTS_KEY, String(value));
};
