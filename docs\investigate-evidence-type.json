{"type": "object", "title": "investigate-evidence-type", "description": "Investigate Evidence Type", "required": ["evidenceType"], "properties": {"badgeId": {"type": "string"}, "cadId": {"type": "string"}, "callerPhoneNumber": {"type": "string"}, "cameraFacingDirection": {"type": "string"}, "cameraPhysicalAddress": {"type": "string"}, "cameraPhysicalLocation": {"type": "string"}, "cameraType": {"type": "string", "enum": ["Doorbell Camera", "Mobile Phone", "Security Camera"]}, "citizenName": {"type": "string"}, "dateOfBirth": {"type": "string"}, "deviceId": {"type": "string"}, "deviceModel": {"type": "string"}, "deviceName": {"type": "string"}, "deviceRegisteredOwner": {"type": "string"}, "deviceType": {"type": "string", "enum": ["Desktop", "Laptop", "Phone", "Tablet", "Other (e.g. iPod Touch)"]}, "evidenceTechnician": {"type": "string"}, "evidenceType": {"type": "string", "enum": ["911 Call Recording", "Arrest Report", "Body Worn Camera", "Booking Photo", "Citizen Submitted Video", "Crime Scene Photo", "In Car Video", "Interview Audio Recording", "Interview Room Recording", "Mobile Device Extraction", "Security Camera Video"]}, "firstName": {"type": "string"}, "interviewRoom": {"type": "string"}, "interviewee": {"type": "string"}, "interviewer": {"type": "string"}, "lastName": {"type": "string"}, "locationTimeline": {"type": "array", "items": {"type": "object", "properties": {"latitude": {"type": "string"}, "longitude": {"type": "string"}}}}, "officerName": {"type": "string"}, "reportNumber": {"type": "string"}, "unitNumber": {"type": "string"}}, "allOf": [{"if": {"properties": {"evidenceType": {"const": "911 Call Recording"}}}, "then": {"required": ["cadId", "callerPhoneNumber"]}}, {"if": {"properties": {"evidenceType": {"const": "Arrest Report"}}}, "then": {"required": ["reportNumber"]}}, {"if": {"properties": {"evidenceType": {"const": "Body Worn Camera"}}}, "then": {"required": ["<PERSON><PERSON><PERSON>", "badgeId", "deviceId", "cameraPhysicalAddress", "locationTimeline"]}}, {"if": {"properties": {"evidenceType": {"const": "Booking Photo"}}}, "then": {"required": ["lastName", "firstName", "dateOfBirth"]}}, {"if": {"properties": {"evidenceType": {"const": "Citizen Submitted Video"}}}, "then": {"required": ["<PERSON><PERSON><PERSON>", "cameraPhysicalAddress", "cameraType"]}}, {"if": {"properties": {"evidenceType": {"const": "Crime Scene Photo"}}}, "then": {"required": ["evidenceTechnician", "badgeId"]}}, {"if": {"properties": {"evidenceType": {"const": "In Car Video"}}}, "then": {"required": ["<PERSON><PERSON><PERSON>", "unitNumber", "deviceId"]}}, {"if": {"properties": {"evidenceType": {"const": "Interview Audio Recording"}}}, "then": {"required": ["interviewer", "interviewee"]}}, {"if": {"properties": {"evidenceType": {"const": "Interview Room Recording"}}}, "then": {"required": ["interviewer", "interviewee", "interviewRoom"]}}, {"if": {"properties": {"evidenceType": {"const": "Mobile Device Extraction"}}}, "then": {"required": ["deviceName", "deviceType", "deviceModel", "deviceRegisteredOwner"]}}, {"if": {"properties": {"evidenceType": {"const": "Security Camera Video"}}}, "then": {"required": ["cameraPhysicalAddress", "cameraPhysicalLocation", "cameraFacingDirection"]}}]}