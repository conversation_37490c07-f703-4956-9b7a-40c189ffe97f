export const enum SearchDataTestSelector {
  ResetAll = 'filter-panel-reset-button',
  SearchInput = 'keyword-search-input',
  SearchTableMenu = 'search-table-menu',
  NavTabSearch = 'nav-tab-search',
  FilterCaseTextField = 'case-info__item-caseId',
  FilterDateRange = 'filter-date-range',
  ConfirmButtonDate = 'confirm-button-date',
  DateRange = 'date-range',
  FilterCognition = 'filter-cognition',
  SearchFilter = 'search-filter',
  ViewSelect = 'view-select',
  TablePaginationContainer = 'table-pagination-container',
  SearchTablePaginationContainer = 'search-table-pagination-container',
  TablePaginationRowsPerPageSelect = 'table-pagination-rows-per-page-select',
  SearchTablePaginationRowsPerPageSelect = 'search-table-pagination-rows-per-page-select',
  TablePaginationNextButton = 'table-pagination-next-button',
  TablePaginationPreviousButton = 'table-pagination-previous-button',
  SearchTablePaginationNextButton = 'search-table-pagination-next-button',
  SearchTablePaginationPreviousButton = 'search-table-pagination-previous-button',
  TablePaginationDisplayedRows = 'table-pagination-displayed-rows',
  SearchTablePaginationDisplayedRows = 'search-table-pagination-displayed-rows',
  ViewTypeToggleButton = 'view-type-toggle-button',
  ListViewIcon = 'list-view-icon',
  GridViewIcon = 'grid-view-icon',
  SearchResultCardView = 'search-result-card-view',
  SearchResultCardViewContent = 'search-result-card-view-content',
  SearchResultListView = 'search-result-list-view',
  SearchButton = 'filter-panel-submit-button',
}

export const titleInSearchPage = [
  'Keyword',
  'Advanced Search',
  'Case Information',
  'Evidence Type',
  'File Type',
];

export const enum DropdownBoxCaseType {
  caseStatus = 'caseStatus',
  fileStatus = 'fileStatus',
}

export enum CognitionPlaceholder {
  filename = 'File Name',
  wordsInTranscription = 'Words in Transcription',
  faceDetections = 'Face Detections',
  objectDescriptors = 'Object Descriptors',
  vehicleRecognition = 'Vehicle Recognition',
  licensePlateRecognition = 'License Plate Recognition',
  textRecognition = 'Text Recognition',
}

export enum EvidenceType {
  callRecording = '911 Call Recording',
  arrestReport = 'Arrest Report',
  bodyWornCamera = 'Body Worn Camera',
  bookingPhoto = 'Booking Photo',
  citizenSubmittedVideo = 'Citizen Submitted Video',
  crimeScenePhoto = 'Crime Scene Photo',
  inCarVideo = 'In Car Video',
  interviewAudioRecording = 'Interview Audio Recording',
  interviewRoomRecording = 'Interview Room Recording',
  interviewVideo = 'Interview Video',
  mobileDeviceExtraction = 'Mobile Device Extraction',
  securityCameraVideo = 'Security Camera Video',
}

export interface CognitionInput {
  placeholder: CognitionPlaceholder;
  value: string;
}

export interface EvidenceTypeInput {
  evidenceType: string;
}

export interface FileTypeInput {
  fileType: string;
}

export const enum GroupView {
  grouped = 'Grouped',
  unGrouped = 'Un-Grouped',
}

export const enum ViewType {
  list = 'list',
  grid = 'grid',
}
