import { configureAppStore } from '@store/index';
import {
  CaseDetailSliceState,
  initialState as caseDetailInitialState,
} from '@store/modules/caseDetail/slice';
import {
  CaseManagerSliceState,
  initialState as CaseManagerState,
} from '@store/modules/caseManager/slice';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { GQLApi } from '@utils/helpers';
import { PENDING_FILE_KEY } from '@utils/saveToLocalStorage';
import { Provider } from 'react-redux';
import { useParams } from 'react-router';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import MoveFileDialog from '.';
import { render } from '../../../test/render';

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useParams: vi.fn(),
  };
});

const defaultMoveFileDialogProps = {
  open: true,
  context: 'search',
  currentFolderId: 'folder-000',
};

const initialStateForMock: {
  caseManager: CaseManagerSliceState;
  caseDetail: CaseDetailSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  caseManager: {
    ...CaseManagerState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'root-folder',
    },
  },
  caseDetail: {
    ...caseDetailInitialState,
    folders: {
      status: 'complete',
      error: undefined,
      data: {
        results: [
          {
            caseId: '',
            statusId: '456',
            caseDate: '2025-02-18T09:11:39.133Z',
            caseName: 'Current case',
            folderId: 'folder-000',
            createdBy: '',
            description: '',
            createdDateTime: '2025-02-18T09:11:50.196Z',
            modifiedDateTime: '2025-02-19T09:05:26.333Z',
            preconfiguredTagIds: [],
            id: 'uuid-000',
          },
          {
            caseId: '',
            statusId: '456',
            caseDate: '2025-02-18T09:11:39.133Z',
            caseName: 'Test case 1',
            folderId: 'folder-001',
            createdBy: '',
            description: '',
            createdDateTime: '2025-02-18T09:11:50.196Z',
            modifiedDateTime: '2025-02-19T09:05:26.333Z',
            preconfiguredTagIds: [],
            id: 'uuid-001',
          },
          {
            caseId: '',
            statusId: '456',
            caseDate: '2025-02-18T09:11:39.133Z',
            caseName: 'Test case 2',
            folderId: 'folder-002',
            createdBy: '',
            description: '',
            createdDateTime: '2025-02-18T09:11:50.196Z',
            modifiedDateTime: '2025-02-19T09:05:26.333Z',
            preconfiguredTagIds: [],
            id: 'uuid-002',
          },
        ],
        totalResults: 2,
        limit: 10,
        from: 0,
        to: 2,
      },
    },
    selectedFileId: 'selectedFileId123',
    currentCaseId: 'folder-000',
    showMoveFileDialog: true,
    destinationCase: {
      id: '',
      status: 'loading',
      data: undefined,
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId',
      tagRegistryId: 'tagRegistryId123',
      evidenceTypeRegistryId: '',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
};

describe('Move File Dialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useParams).mockReturnValue({ selectedId: 'folder-000' });
  });

  it('renders the MoveFileDialog', () => {
    const store = configureAppStore(initialStateForMock);
    const { container } = render(
      <Provider store={store}>
        <MoveFileDialog {...defaultMoveFileDialogProps} />
      </Provider>
    );

    expect(container).toBeTruthy();
    expect(screen.getByText('Move to Case')).toBeTruthy();
  });

  it('click Move button should call moveFile API', async () => {
    const moveFile = vi.spyOn(GQLApi.prototype, 'moveFile');
    const setItemSpy = vi.spyOn(Storage.prototype, 'setItem');

    const store = configureAppStore(initialStateForMock);
    render(
      <Provider store={store}>
        <MoveFileDialog {...defaultMoveFileDialogProps} />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('move-file-dialog')).toBeInTheDocument();
    });

    fireEvent.mouseDown(screen.getByTestId('ArrowDropDownIcon'));

    await waitFor(() => {
      expect(screen.getByText('Test case 1')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Test case 1'));

    await waitFor(() => {
      expect(screen.getByTestId('confirm-button')).toBeEnabled();
    });

    fireEvent.click(screen.getByTestId('confirm-button'));

    await waitFor(() => {
      expect(moveFile).toHaveBeenCalledWith({
        fileId: 'selectedFileId123',
        oldFolderId: 'folder-000',
        newFolderId: 'folder-001',
      });
    });

    // Check that localStorage was updated with the correct values
    const expiry = JSON.parse(setItemSpy.mock.calls[0][1])[0].expiry;
    await waitFor(() => {
      expect(setItemSpy).toHaveBeenCalledWith(
        PENDING_FILE_KEY,
        JSON.stringify([
          {
            value: {
              fileId: 'selectedFileId123',
              oldFolderId: 'folder-000',
              newFolderId: 'folder-001',
            },
            expiry,
          },
        ])
      );
    });
  });

  it('enables the Move button when a folder is selected', async () => {
    const store = configureAppStore(initialStateForMock);
    render(
      <Provider store={store}>
        <MoveFileDialog {...defaultMoveFileDialogProps} />
      </Provider>
    );

    fireEvent.mouseDown(screen.getByTestId('ArrowDropDownIcon'));

    await waitFor(() => {
      expect(screen.getByText('Test case 1')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Test case 1'));

    await waitFor(() => {
      expect(screen.getByTestId('confirm-button')).toBeEnabled();
    });
  });

  it('disables the Move button when no folder is deleted', async () => {
    const store = configureAppStore(initialStateForMock);
    render(
      <Provider store={store}>
        <MoveFileDialog {...defaultMoveFileDialogProps} />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('confirm-button')).toBeDisabled();
    });
  });

  it('closes the Move file dialog when the Close button is clicked', async () => {
    const store = configureAppStore(initialStateForMock);
    render(
      <Provider store={store}>
        <MoveFileDialog {...defaultMoveFileDialogProps} />
      </Provider>
    );
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByText('Select a Folder')).not.toBeInTheDocument();
    });
  });
});
