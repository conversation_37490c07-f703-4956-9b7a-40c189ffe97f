{"name": "investigate-backend", "version": "1.0.0", "scripts": {"start": "node dist/server.js", "start:dev": "ts-node-dev src/server.ts", "start:debug": "ts-node-dev --inspect-brk -- src/server.ts", "build": "rimraf dist & tsc", "lint": "yarn lint:es && yarn lint:tsc && yarn lint:prettier", "lint:es": "eslint . --max-warnings 0", "lint:tsc": "tsc --noEmit", "lint:prettier": "prettier src --check", "format": "prettier  src --write", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"body-parser": "^2.2.0", "express": "^5.1.0"}, "devDependencies": {"@eslint/js": "^9.25.1", "@types/express": "^5.0.1", "@types/node": "^24.0.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "prettier": "^3.5.3", "rimraf": "^6.0.1", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}}