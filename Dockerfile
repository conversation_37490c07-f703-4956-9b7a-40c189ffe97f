# Define build type
ARG K8S_BUILD=FALSE
ARG BASE_IMAGE=registry.central.aiware.com/fed-nginx:1.27.3
ARG NODE_VERSION=20

FROM node:${NODE_VERSION} AS frontend

ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/" && \
    mkdir -p /app && \
    rm -rf /vars/lib/apt/lists/*
COPY . /app
WORKDIR /app/client

RUN ls -a && \
    chmod +x /app/scripts/*.sh && \
    yarn && \
    yarn build

RUN echo '### /app/scripts/buildinfo.sh...' && /app/scripts/buildinfo.sh

FROM node:${NODE_VERSION} AS backend
ARG GITHUB_ACCESS_TOKEN
RUN apt-get update && apt-get install -y --no-install-recommends ca-certificates jq && \
    git config --global url."https://${GITHUB_ACCESS_TOKEN}:<EMAIL>/".insteadOf "https://github.com/"

RUN mkdir -p /app/server
RUN rm -rf /vars/lib/apt/lists/*
WORKDIR /app/server

RUN echo "//npm.pkg.github.com/:_authToken=${GITHUB_ACCESS_TOKEN}\n" >> ~/.npmrc
COPY server/ .

RUN yarn
RUN yarn build

# Set the final base image based on build arg BASE_NAME
FROM ${BASE_IMAGE} AS final

# Set the user to root
USER root

ARG K8S_BUILD
ARG NODE_VERSION
ENV APPLICATION=investigate-app

# Check if K8S_BUILD is TRUE or FALSE and check if the package manager is apk or dnf
RUN if [ "$K8S_BUILD" = "FALSE" ]; then \
    if command -v apk > /dev/null; then \
        apk update && \
        apk add --no-cache jq curl bash nodejs npm && \
        apk add --no-cache --upgrade pcre libjpeg-turbo libxml2 ncurses curl; \
    elif command -v dnf > /dev/null; then \
        dnf update -y && \
        dnf module -y enable nodejs:${NODE_VERSION} && \
        dnf install -y jq curl bash nodejs npm --allowerasing && \
        dnf install -y pcre libjpeg-turbo libxml2 ncurses curl&& \
        dnf clean all; \
    else \
        echo "Neither apk nor dnf found, exiting"; \
        exit 1; \
    fi; \
    else \
    if command -v apk > /dev/null; then \
        apk update && \
        apk add --no-cache nginx envsubst && \
        mkdir -p /etc/nginx/conf.d && \
        apk add --no-cache jq curl bash nodejs npm && \
        apk add --no-cache --upgrade pcre libjpeg-turbo libxml2 ncurses curl; \
    elif command -v dnf > /dev/null; then \
        dnf update -y && \
        dnf module -y enable nodejs:${NODE_VERSION} && \
        dnf install -y nginx gettext && \
        mkdir -p /etc/nginx/conf.d && \
        dnf install -y jq curl bash nodejs npm --allowerasing && \
        dnf install -y pcre libjpeg-turbo libxml2 ncurses curl && \
        dnf clean all; \
    else \
        echo "Neither apk nor dnf found, exiting"; \
        exit 1; \
    fi; \
    fi

ENV NGINX_PORT=9000

EXPOSE ${NGINX_PORT}/tcp

COPY --from=frontend /app/scripts/getconfig-dynamicConfig.sh /getconfig-dynamicConfig.sh
COPY --from=frontend /app/scripts/dynamicConfig-index-html.sh /dynamicConfig-index-html.sh
COPY --from=frontend /app/scripts/entrypoint.sh /entrypoint.sh
COPY --from=frontend /app/scripts/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=frontend /app/client/configWhitelist.json /configWhitelist.json
COPY --from=frontend /app/client/dist /usr/share/nginx/html
COPY --from=frontend /app/client/build-manifest.yml /build-manifest.yml
COPY --from=frontend /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt

COPY --from=backend /app/server/ /server/
COPY --from=backend /app/server/apiConfigWhitelist.json /apiConfigWhitelist.json

COPY --from=veritone/aiware-spa:sha-ceadca5 /usr/share/nginx/config /usr/share/nginx/html/config
COPY --from=veritone/aiware-spa:sha-ceadca5 /usr/share/nginx/config /server/config

ENTRYPOINT ["/entrypoint.sh"]
