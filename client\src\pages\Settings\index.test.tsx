import { configureAppStore } from '@store/index';
import {
  initialState as caseManagerInitialState,
  CaseManagerSliceState,
} from '@store/modules/caseManager/slice';
import { ConfigSliceState } from '@store/modules/config/slice';
import {
  fetchStatuses,
  initialState as settingsInitialState,
  SettingsSliceState,
} from '@store/modules/settings/slice';
import { fireEvent, screen, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GQLApi } from '@utils/helpers';
import { Provider } from 'react-redux';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import Settings from '.';
import { render } from '../../../test/render';

const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  appConfig: ConfigSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: settingsInitialState,
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
    settingsPollInterval: 12000,
    caseStatusTagsPollInterval: 12000,
    casePollingInterval: 12000,
    filePollingInterval: 12000,
  },
  auth: { sessionToken: 'sessionToken123' },
  caseManager: caseManagerInitialState,
  appConfig: {
    statusSchema: {
      status: 'complete',
      error: '',
      id: 'statusSchema123',
    },
    tagSchema: {
      status: 'complete',
      error: '',
      id: 'tagSchema123',
    },
    evidenceTypeSchema: {
      status: 'complete',
      error: '',
      id: 'evidenceTypeSchema123',
    },
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      evidenceTypeRegistryId: 'evidenceTypeRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
  },
};

const mockFetchStatus = {
  id: '6d3fa632-98d5-4659-9214-5c025eed2a6e',
  data: {
    statuses: [
      {
        id: 'newLabel1',
        color: '#4F46E5',
        label: 'label1',
        active: true,
      },
      {
        id: 'newOpen1',
        color: '#4F46E5',
        label: 'Open1',
        active: true,
      },
    ],
    createdBy: '',
  },
};

const mockFetchTags = {
  id: 'e4a3eaa7-785e-49b0-8ab5-a14e92a2c40d',
  data: {
    tags: [
      {
        id: 'tag1',
        label: 'Tag 1',
        active: true,
      },
      {
        id: 'tag2',
        label: 'Tag 2',
        active: true,
      },
      {
        id: 'tag3',
        label: 'Tag 3',
        active: false,
      },
    ],
    createdBy: '',
  },
};

const mockGetLastestTagsSdoId = {
  id: 'e4a3eaa7-785e-49b0-8ab5-a14e92a2c40d',
  data: {},
};

const mockGetLastestTagsDifferentSdoId = {
  id: 'd573ebb7-785e-49b0-8ab5-t16e92a2c456',
  data: {},
};

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useBlocker: vi.fn(() => ({
      state: 'unblocked',
    })),
    useBeforeUnload: vi.fn(),
  };
});

describe('Settings', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should renders correctly without edit-mode', async () => {
    const store = configureAppStore(initialStateForMock);
    vi.spyOn(GQLApi.prototype, 'getSDOSchemaId').mockImplementationOnce(() =>
      Promise.resolve('sdoSchemaId456')
    );
    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockFetchStatus)
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    expect(screen.getByText('Settings')).toBeTruthy();

    await waitFor(() => {
      expect(screen.getByText('Status name')).toBeInTheDocument();
    });

    expect(
      screen.getByRole('button', { name: /add status/i, hidden: true })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /edit status labels/i, hidden: true })
    ).toBeInTheDocument();
  });

  it('should renders correctly with edit-mode', async () => {
    const store = configureAppStore(initialStateForMock);
    vi.spyOn(GQLApi.prototype, 'getSDOSchemaId').mockImplementationOnce(() =>
      Promise.resolve('sdoSchemaId456')
    );
    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockFetchStatus)
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByText('Status name')).toBeInTheDocument();
    });

    // open edit mode
    fireEvent.click(
      screen.getByRole('button', {
        name: /edit status labels/i,
        hidden: true,
      })
    );
    expect(
      within(screen.getByTestId('settings-content')).getByRole('button', {
        name: /save changes/i,
        hidden: true,
      })
    ).toBeDisabled();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();

    // select a row
    fireEvent.click(
      within(
        screen.getByRole('row', {
          name: /toggle select row label1/i,
          hidden: true,
        })
      ).getByRole('checkbox', { name: /toggle select row/i, hidden: true })
    );

    expect(
      screen.getByRole('button', { name: /delete/i, hidden: true })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /set visibility/i, hidden: true })
    ).toBeInTheDocument();
    expect(
      screen.getByRole('button', { name: /set color/i, hidden: true })
    ).toBeInTheDocument();
  });

  it('Switch to Preconfigured Tags tab and show Circular Progress', async () => {
    render(<Settings />);

    const statusLabelsTab = screen.getByTestId('status-labels-tab');
    expect(statusLabelsTab).toBeEnabled();

    const preconfiguredTagsTab = screen.getByTestId('preconfigured-tags-tab');
    userEvent.click(preconfiguredTagsTab);

    await waitFor(() => {
      expect(screen.getByText('Preconfigured Tags')).toBeInTheDocument();
    });

    expect(screen.getByText('Pre-Configured Tag Settings')).toBeInTheDocument();
    expect(screen.getByTestId('reorder-table-loading')).toBeInTheDocument();
  });

  it('Updates tags visibility on a row in edit mode', async () => {
    const store = configureAppStore(initialStateForMock);
    const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );
    vi.spyOn(GQLApi.prototype, 'createStructuredData').mockResolvedValue({
      id: 'tagSchema123',
      data: {
        createdBy: 'testUser',
        tags: [
          { active: false, id: 'tag1', label: 'Tag 1' },
          { active: true, id: 'tag2', label: 'Tag 2' },
          { active: false, id: 'tag3', label: 'Tag 3' },
        ],
      },
    });

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    expect(screen.getByText('Settings')).toBeTruthy();

    // Navigate to Tag tab
    userEvent.click(screen.getByTestId('preconfigured-tags-tab'));
    await waitFor(() => {
      expect(screen.getByText('Preconfigured Tags')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('edit-button'));

    // Select a row's by click on its checkbox
    const checkboxes = screen.getAllByTestId('row-check-box');
    await waitFor(() => {
      expect(checkboxes.length).toBeGreaterThan(0);
    });
    fireEvent.click(
      within(checkboxes[0]).getByRole('checkbox', {
        name: /toggle select row/i,
        hidden: true,
      })
    );

    // After select one row, the 'Set visibility' should be enabled
    await waitFor(() => {
      expect(screen.getByTestId('change-visibility-button')).toBeEnabled();
    });
    fireEvent.click(screen.getByTestId('change-visibility-button'));

    // Select radio button when dialog is open and confirm
    await waitFor(() => {
      expect(screen.getByText(/set tag visibility/i)).toBeInTheDocument();
    });
    fireEvent.click(screen.getByTestId('inactive-radio'));
    fireEvent.click(screen.getByRole('button', { name: /apply/i }));

    const saveChangesButton = within(
      screen.getByText(/pre\-configured tag settings/i)
    ).getByRole('button', {
      name: /save changes/i,
      hidden: true,
    });

    expect(saveChangesButton).toBeEnabled();
    fireEvent.click(saveChangesButton);

    // Expect API save tags to be called
    await waitFor(() => {
      expect(saveTags).toHaveBeenCalledWith({
        schemaId: 'tagSchema123',
        data: {
          createdBy: undefined,
          tags: [
            {
              active: false, // This tag is set to inactive
              id: 'tag1',
              label: 'Tag 1',
            },
            {
              active: true, // This tag remains active
              id: 'tag2',
              label: 'Tag 2',
            },
            {
              active: false, // This tag is set to inactive
              id: 'tag3',
              label: 'Tag 3',
            },
          ],
        },
      });
    });
  });

  it('shows confirmation dialog when switching tabs with unsaved changes', async () => {
    const store = configureAppStore(initialStateForMock);

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );
    await waitFor(() => {
      expect(screen.getByTestId('edit-button')).toBeInTheDocument();
    });
    const editButton = screen.getByTestId('edit-button');
    userEvent.click(editButton);

    const inputs = screen.getAllByTestId('reorder-table-name-edit');
    const ourInput = inputs[0].querySelector('input') as HTMLInputElement;

    expect(ourInput).not.toBeNull();
    userEvent.type(ourInput, 'updateStatus');

    const preConfiguredTagSettingsTab = screen.getByTestId(
      'preconfigured-tags-tab'
    );
    userEvent.click(preConfiguredTagSettingsTab);
    await waitFor(() => {
      expect(
        screen.getByText('Would you like to save your changes before closing?')
      ).toBeInTheDocument();
    });
  });

  it('delete multiple tags', async () => {
    const store = configureAppStore(initialStateForMock);
    const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.clearAllMocks();
    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockFetchTags)
    );
    vi.spyOn(GQLApi.prototype, 'createStructuredData').mockResolvedValue({
      id: 'tagSchema123',
      data: {
        createdBy: 'testUser',
        tags: [],
      },
    });

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    expect(screen.getByText('Settings')).toBeTruthy();

    const prefconfiguredTagsTab = screen.getByTestId('preconfigured-tags-tab');
    fireEvent.click(prefconfiguredTagsTab);

    await waitFor(() => {
      expect(
        screen.getByText('Pre-Configured Tag Settings')
      ).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('edit-button'));
    const firstCheckbox = within(
      screen.getByRole('row', {
        name: /toggle select row tag 1/i,
        hidden: true,
      })
    ).getByRole('checkbox', {
      name: /toggle select row/i,
      hidden: true,
    });
    const secondCheckbox = within(
      screen.getByRole('row', {
        name: /toggle select row tag 2/i,
        hidden: true,
      })
    ).getByRole('checkbox', {
      name: /toggle select row/i,
      hidden: true,
    });

    fireEvent.click(firstCheckbox);
    fireEvent.click(secondCheckbox);
    const deleteButton = screen.getByRole('button', {
      name: /delete/i,
      hidden: true,
    });
    expect(deleteButton).toBeInTheDocument();

    fireEvent.click(deleteButton);
    fireEvent.click(
      screen.getByRole('button', {
        name: /delete/i,
      })
    );

    await waitFor(() => {
      expect(screen.getByTestId('reorder-table-row-tag1')).toHaveClass(
        'row-deleted'
      );
      expect(screen.getByTestId('reorder-table-row-tag2')).toHaveClass(
        'row-deleted'
      );
    });

    fireEvent.click(screen.getByTestId('edit-button'));
    waitFor(() => {
      expect(saveTags).toHaveBeenCalledOnce();
    });
  });

  it('deletes a tag', async () => {
    const store = configureAppStore(initialStateForMock);
    const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockGetLastestTagsSdoId)
    );

    vi.clearAllMocks();
    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockFetchTags)
    );

    vi.spyOn(GQLApi.prototype, 'createStructuredData').mockResolvedValue({
      id: 'tagSchema123',
      data: {
        createdBy: 'testUser',
        tags: [
          { active: true, id: 'tag2', label: 'Tag 2' },
          { active: false, id: 'tag3', label: 'Tag 3' },
        ],
      },
    });

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    expect(screen.getByText('Settings')).toBeTruthy();

    const prefconfiguredTagsTab = screen.getByTestId('preconfigured-tags-tab');
    fireEvent.click(prefconfiguredTagsTab);

    await waitFor(() => {
      expect(
        screen.getByText('Pre-Configured Tag Settings')
      ).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('edit-button'));

    const removeButton = screen.getAllByTestId('reorder-table__remove-button');
    expect(removeButton.length).toBe(3);

    fireEvent.click(removeButton[0]);
    fireEvent.click(
      screen.getByRole('button', {
        name: /delete/i,
      })
    );

    await waitFor(() => {
      expect(screen.getByTestId('reorder-table-row-tag1')).toHaveClass(
        'row-deleted'
      );
    });

    fireEvent.click(screen.getByTestId('edit-button'));

    waitFor(() => {
      expect(saveTags).toHaveBeenCalledOnce();
    });
  });

  it('shows concurrent modification error on save with stale sdoId', async () => {
    const store = configureAppStore(initialStateForMock);
    const saveTags = vi
      .spyOn(GQLApi.prototype, 'createSDO')
      .mockImplementation(() =>
        Promise.resolve(mockGetLastestTagsDifferentSdoId)
      );
    vi.spyOn(GQLApi.prototype, 'getSDOSchemaId')
      .mockImplementationOnce(() => Promise.resolve('sdoSchemaId123'))
      .mockImplementationOnce(() => Promise.resolve('sdoSchemaId456'));

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockGetLastestTagsDifferentSdoId)
    );

    vi.clearAllMocks();
    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockFetchTags)
    );
    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    expect(screen.getByText('Settings')).toBeTruthy();

    const prefconfiguredTagsTab = screen.getByTestId('preconfigured-tags-tab');
    fireEvent.click(prefconfiguredTagsTab);

    await waitFor(() => {
      expect(
        screen.getByText('Pre-Configured Tag Settings')
      ).toBeInTheDocument();
    });

    fireEvent.click(screen.getByTestId('edit-button'));

    const removeButton = screen.getAllByTestId('reorder-table__remove-button');
    expect(removeButton.length).toBe(3);

    fireEvent.click(removeButton[0]);

    // click confirmation delete dialog delete button
    fireEvent.click(
      screen.getByRole('button', {
        name: /delete/i,
      })
    );

    await waitFor(() => {
      expect(screen.getByTestId('reorder-table-row-tag1')).toHaveClass(
        'row-deleted'
      );
    });

    // Click save
    fireEvent.click(screen.getByTestId('edit-button'));

    // save tags is not called because the system passed a stale sdoId
    expect(saveTags).not.toHaveBeenCalled();
  });

  it('should show error when trying to add a duplicate status', async () => {
    const store = configureAppStore(initialStateForMock);

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('add-button')).toBeInTheDocument();
    });

    const addButton = screen.getByTestId('add-button');
    userEvent.click(addButton);
    const input = screen.getByTestId('status-name-input');
    userEvent.type(input, 'Open1');
    await waitFor(() => {
      expect(
        screen.getByText('Status label name already taken.')
      ).toBeInTheDocument();
    });
  });

  it('enables status edit mode on click', async () => {
    const store = configureAppStore(initialStateForMock);
    // const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('edit-button')).toBeInTheDocument();
    });

    const editButton = screen.getByTestId('edit-button');
    expect(editButton.textContent).toContain('Edit Status Labels');
    userEvent.click(editButton);
    expect(editButton.textContent).toContain('Save Changes');
  });

  it('disables save changes button when no changes are made', async () => {
    const store = configureAppStore(initialStateForMock);
    // const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('edit-button')).toBeInTheDocument();
    });

    // Click on the edit button to go into edit mode
    const editButton = screen.getByTestId('edit-button');
    userEvent.click(editButton);

    // Now the edit button has changed to 'Save Changes'
    expect(editButton.textContent).toContain('Save Changes');

    // and it should be disabled, since no changes have been made
    expect(editButton).toBeDisabled();

    // Make some changes in status name input
    const inputs = screen.getAllByTestId('reorder-table-name-edit');
    const ourInput = inputs[0].querySelector('input') as HTMLInputElement;
    expect(ourInput).not.toBeNull();
    userEvent.type(ourInput, 'update status');

    expect(ourInput).toBeEnabled();
  });

  it('should show error when trying to add a Invalid Tag', async () => {
    const store = configureAppStore(initialStateForMock);

    const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );

    vi.spyOn(GQLApi.prototype, 'createStructuredData').mockResolvedValue({
      id: 'tagSchema123',
      data: {
        createdBy: 'testUser',
        tags: [
          { active: true, id: 'tag1', label: 'Tag 1' },
          { active: true, id: 'tag2', label: 'Tag 2' },
          { active: false, id: 'tag3', label: 'Tag 3' },
        ],
      },
    });

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    expect(screen.getByText('Settings')).toBeTruthy();

    const prefconfiguredTagsTab = screen.getByTestId('preconfigured-tags-tab');
    userEvent.click(prefconfiguredTagsTab);

    await waitFor(() => {
      expect(screen.getByText('Preconfigured Tags')).toBeInTheDocument();
    });

    const addButton = screen.getByTestId('add-button');
    userEvent.click(addButton);

    const input = screen.getByTestId('tag-name-input');

    // Duplicate Tag
    fireEvent.change(input, { target: { value: 'Tag 1' } });
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(
        screen.getByText('The following tags already exist: Tag 1')
      ).toBeInTheDocument();
    });

    // Invalid Tag contains special characters
    fireEvent.change(input, { target: { value: 'Tag *%#$#@' } });
    await waitFor(() => {
      expect(screen.getByTestId('error-message')).toBeInTheDocument();
    });
    await waitFor(() => {
      expect(
        screen.getByText(
          'Invalid character in Tag *%#$#@. Only letters, numbers, _ , and - allowed.'
        )
      ).toBeInTheDocument();
    });

    // Valid Tag, this will not show any error
    fireEvent.change(input, { target: { value: 'New Tag' } });
    await waitFor(() => {
      expect(screen.queryByTestId('error-message')).not.toBeInTheDocument();
    });
    fireEvent.click(screen.getByRole('button', { name: /add tag/i }));
    waitFor(() => {
      expect(saveTags).toHaveBeenCalled();
    });
  });

  it('move to the newly selected tab after saving unsaved changes from confirmation dialog', async () => {
    const store = configureAppStore(initialStateForMock);
    // const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );

    vi.spyOn(GQLApi.prototype, 'createStructuredData').mockResolvedValue({
      id: 'tagSchema123',
      data: {
        createdBy: 'testUser',
        tags: [
          { active: true, id: 'tag1', label: 'Tag 1' },
          { active: true, id: 'tag2', label: 'Tag 2' },
          { active: false, id: 'tag3', label: 'Tag 3' },
        ],
      },
    });

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    store.dispatch(fetchStatuses({}));

    await waitFor(() => {
      expect(screen.getByTestId('edit-button')).toBeInTheDocument();
    });

    // Click on the edit button to go into edit mode
    const editButton = screen.getByTestId('edit-button');
    userEvent.click(editButton);

    // Now the edit button has changed to 'Save Changes'
    expect(editButton.textContent).toContain('Save Changes');

    // and it should be disabled, since no changes have been made
    expect(editButton).toBeDisabled();

    // Make some changes in status name input
    const first = within(
      screen.getByRole('cell', { name: /label1/i, hidden: true })
    ).getByRole('textbox', { hidden: true });
    fireEvent.change(first, {
      target: { value: 'updateStatus' },
    });

    // Navigate to Tag tab
    const preconfiguredTagsTab = screen.getByTestId('preconfigured-tags-tab');
    userEvent.click(preconfiguredTagsTab);

    // Expect confirmation dialog to show up
    await waitFor(() => {
      expect(
        screen.getByText('Would you like to save your changes before closing?')
      ).toBeInTheDocument();
    });

    // Confirm saving changes
    const confirmButton = screen.getByTestId('confirm-button');
    userEvent.click(confirmButton);

    await waitFor(() => {
      // Expect to be on the tag tab after saving changes
      expect(
        screen.getByText('Pre-Configured Tag Settings')
      ).toBeInTheDocument();
    });
  });

  it('adds multiple tags when the user types a comma-separated list of tags in Add Tags dialog.', async () => {
    const store = configureAppStore(initialStateForMock);

    const saveTags = vi
      .spyOn(GQLApi.prototype, 'createSDO')
      .mockImplementation(() => Promise.resolve(mockFetchTags));

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation(() =>
      Promise.resolve(mockFetchTags)
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    expect(screen.getByText('Settings')).toBeTruthy();

    const prefconfiguredTagsTab = screen.getByTestId('preconfigured-tags-tab');
    userEvent.click(prefconfiguredTagsTab);

    await waitFor(() => {
      expect(screen.getByText('Preconfigured Tags')).toBeInTheDocument();
    });

    const addButton = screen.getByTestId('add-button');
    userEvent.click(addButton);

    const input = screen.getByTestId('tag-name-input');

    // Input multiple tags separated by commas
    fireEvent.change(input, {
      target: { value: 'TuckerTag, LukeTag' },
    });

    // The confirm button should now read 'Add Tags', since we are adding multiple tags
    const confirmBtn = screen.getByRole('button', { name: /add tags/i });
    expect(confirmBtn).toBeEnabled();

    fireEvent.click(confirmBtn);
    // The two tags added above should be sent to saveTags
    await waitFor(() => {
      expect(saveTags).toHaveBeenCalledWith({
        schemaId: 'tagSchema123',
        data: {
          createdBy: undefined,
          tags: [
            {
              active: true,
              id: expect.any(String),
              label: 'TuckerTag',
            },
            {
              active: true,
              id: expect.any(String),
              label: 'LukeTag',
            },
            {
              active: true,
              id: 'tag1',
              label: 'Tag 1',
            },
            {
              active: true,
              id: 'tag2',
              label: 'Tag 2',
            },
            {
              active: false,
              id: 'tag3',
              label: 'Tag 3',
            },
          ],
        },
      });
    });
  });

  it('shows error upon typing invalid status names in edit mode', async () => {
    const store = configureAppStore(initialStateForMock);
    // const saveTags = vi.spyOn(GQLApi.prototype, 'createSDO');

    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementation((params) =>
      Promise.resolve(
        params === 'statusSchema123' ? mockFetchStatus : mockFetchTags
      )
    );

    render(
      <Provider store={store}>
        <Settings />
      </Provider>
    );

    await waitFor(() => {
      expect(screen.getByTestId('edit-button')).toBeInTheDocument();
    });

    // Click on the edit button to go into edit mode
    const editButton = screen.getByTestId('edit-button');
    userEvent.click(editButton);

    // Now the edit button has changed to 'Save Changes'
    expect(editButton.textContent).toContain('Save Changes');

    // and it should be disabled, since no changes have been made
    expect(editButton).toBeDisabled();

    // Make some changes in status name input
    const inputs = screen.getAllByTestId('reorder-table-name-edit');
    const ourInput = inputs[0].querySelector('input') as HTMLInputElement;
    expect(ourInput).not.toBeNull();
    userEvent.type(ourInput, 'completed&'); // Invalid character "&"

    // Check if the error message is displayed
    await waitFor(() => {
      expect(
        screen.getByText('Invalid character only "-" and "_" are allowed.')
      ).toBeInTheDocument();
    });

    // And because of the error, the save button should be disabled
    expect(editButton).toBeDisabled();
  });
});
