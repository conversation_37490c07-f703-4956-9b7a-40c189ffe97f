import { DataTestSelector } from '../support/helperFunction/caseManagerHelper';

export const caseDetailPage = {
  caseDetailNameText: () =>
    cy
      .getDataIdCy({ idAlias: DataTestSelector.CaseDetailContent })
      .find('[data-testid="case-name"]'),
  verifyCaseDetailName: (caseName: string) =>
    caseDetailPage.caseDetailNameText().should('contain.text', caseName),

  verifyTagInDetails: (text: string) =>
    cy
      .getDataIdCy({ idAlias: DataTestSelector.CaseDetailContent })
      .within(() => {
        cy.get('[data-testid^="case-tag"]').should('contain.text', text);
      }),

  verifyStatusInDetails: (text: string) =>
    cy
      .getDataIdCy({ idAlias: DataTestSelector.CaseDetailContent })
      .within(() => {
        cy.get('[data-testid^="case-status-button"]').should(
          'contain.text',
          text
        );
      }),

  getFileRowByName: (fileRowName: string) =>
    cy.get('[data-testid^="table-row-"]', { timeout: 60000 }).then(($rows) => {
      const matchedRow = $rows.toArray().find((tr) => {
        const rowText = Cypress.$(tr).text().trim();
        return rowText.includes(fileRowName);
      });
      return cy.wrap(matchedRow);
    }),

  verifyFileInTable: (caseName: string) => {
    cy.get('[data-testid^="table-row-"]', { timeout: 60000 })
      .contains(caseName)
      .should('be.visible');
  },
  openCaseDetailBtn: () => {
    cy.getDataIdCy({ idAlias: 'view-case-details-button' }).click();
  },

  openKebabMenuForFile: (caseName: string) => {
    caseDetailPage.getFileRowByName(caseName).click();
    cy.awaitNetworkResponseCode({ alias: '@fetchDetailPopup', code: 200 });
    caseDetailPage
      .getFileRowByName(caseName)
      .within(() =>
        cy
          .getDataIdCy({ idAlias: DataTestSelector.SearchTableMenu })
          .click({ force: true })
      );
  },

  sortTableColumn: (label: string, orderBy: string) => {
    cy.getDataIdCy({ idAlias: `sort-label-${label}` }).as('tableHeading');

    cy.get('@tableHeading').then(($heading) => {
      if (orderBy === 'a-z') {
        if (
          $heading.attr('aria-sort') === 'descending' ||
          !$heading.attr('aria-sort')
        ) {
          cy.get('@tableHeading').click({ scrollBehavior: false });
          cy.get('@tableHeading').trigger('mouseover');
          cy.get('@tableHeading').within(() => {
            cy.getDataIdCy({ idAlias: 'ArrowDropUpIcon' }).should('be.visible');
          });
        }

        cy.wrap($heading).should('have.attr', 'aria-sort', 'ascending');
      } else if (orderBy === 'z-a') {
        if (
          $heading.attr('aria-sort') === 'ascending' ||
          !$heading.attr('aria-sort')
        ) {
          cy.get('@tableHeading').click({ scrollBehavior: false });
          cy.get('@tableHeading').trigger('mouseover');
          cy.get('@tableHeading').within(() => {
            cy.getDataIdCy({ idAlias: 'ArrowDropDownIcon' }).should(
              'be.visible'
            );
          });
        }

        cy.wrap($heading).should('have.attr', 'aria-sort', 'descending');
      }
    });

    cy.assertNoLoading();
  },
};
