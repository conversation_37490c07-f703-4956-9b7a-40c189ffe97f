import './index.scss';
import {
  <PERSON>ton,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  IconButton,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useSelector } from 'react-redux';
import {
  addFilesToCase,
  selectSelectedResults,
  selectShowAddToCaseDialog,
  setShowAddToCaseDialog,
  updateSelectedResults,
} from '../../store/modules/search/slice';
import { useAppDispatch } from '../../store/hooks';
import { I18nTranslate } from '@i18n';
import {
  searchFolders,
  selectFolders,
} from '../../store/modules/caseDetail/slice';
import { useCallback, useEffect, useMemo, useState } from 'react';
import AutocompleteVirtualizedSelect from '@components/AutocompleteVirtualizedSelect';
import { selectFolderContentTemplateSchema } from '../../store/modules/caseManager/slice';

const AddToCaseDialog = () => {
  const intl = I18nTranslate.Intl();
  const dispatch = useAppDispatch();
  const showAddToCaseDialog = useSelector(selectShowAddToCaseDialog);
  const caseFolders = useSelector(selectFolders);
  const folderContentTemplateSchemaId = useSelector(
    selectFolderContentTemplateSchema
  ).id;
  const selectedResults = useSelector(selectSelectedResults);
  const [selectedCaseId, setSelectedCaseId] = useState<string | undefined>(
    undefined
  );

  const hasRowWithCaseId = useMemo(
    () => selectedResults.some((file) => file.caseId),
    [selectedResults]
  );

  const handleClose = () => {
    dispatch(setShowAddToCaseDialog(false));
    setSelectedCaseId(undefined);
  };

  useEffect(() => {
    if (caseFolders.status === 'idle' && folderContentTemplateSchemaId) {
      dispatch(
        searchFolders({
          offset: 0,
          limit: 30,
        })
      );
    }
  }, [caseFolders, folderContentTemplateSchemaId]);

  const handleCaseChange = (value: string) => {
    setSelectedCaseId(value);
  };

  const handleConfirm = () => {
    if (selectedCaseId && selectedResults.length > 0) {
      const selectedFileIds = selectedResults.map((file) => file.id);

      dispatch(
        addFilesToCase({ caseId: selectedCaseId, fileIds: selectedFileIds })
      );
      dispatch(updateSelectedResults([]));
    }
    setSelectedCaseId(undefined);
  };

  const loadMoreItems = useCallback(() => {
    dispatch(
      searchFolders({
        offset: caseFolders.data.to + 1,
        limit: caseFolders.data.limit,
      })
    );
  }, [dispatch, caseFolders.data.to, caseFolders.data.limit]);

  return (
    <Dialog
      open={showAddToCaseDialog}
      onClose={handleClose}
      fullWidth
      className="add-to-case-dialog"
    >
      <div
        className="add-to-case-dialog__header"
        data-testid="add-to-case-dialog"
      >
        <div className="add-to-case-dialog__title">
          {hasRowWithCaseId
            ? I18nTranslate.TranslateMessage('selectAFolder')
            : I18nTranslate.TranslateMessage('addToCase')}
        </div>
        <IconButton
          aria-label={intl.formatMessage({ id: 'close' })}
          onClick={handleClose}
        >
          <CloseIcon />
        </IconButton>
      </div>

      <div className="add-to-case-dialog__sub_title">
        {hasRowWithCaseId
          ? I18nTranslate.TranslateMessage('chooseCase')
          : I18nTranslate.TranslateMessage('chooseCaseAddFiles')}
      </div>

      <DialogContent className="add-to-case-dialog__content">
        {caseFolders.data.results.length === 0 &&
        caseFolders.status === 'loading' ? (
          <div className="loader-wrapper">
            <CircularProgress />
          </div>
        ) : (
          <AutocompleteVirtualizedSelect
            options={caseFolders.data.results.map((folder) => ({
              id: folder.folderId,
              label: folder.caseName,
            }))}
            value={selectedCaseId}
            hasMore={caseFolders.data.totalResults > caseFolders.data.to}
            loadMoreItems={loadMoreItems}
            placeholder={intl.formatMessage({ id: 'selectACasePlaceholder' })}
            onChange={handleCaseChange}
            loading={caseFolders.status}
          />
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} className="cancel-button">
          {I18nTranslate.TranslateMessage('close')}
        </Button>
        <Button
          onClick={handleConfirm}
          disabled={!(selectedCaseId && selectedResults.length > 0)}
          className="confirm-button"
          data-testid="confirm-button"
        >
          {hasRowWithCaseId
            ? I18nTranslate.TranslateMessage('move')
            : I18nTranslate.TranslateMessage('add')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AddToCaseDialog;
