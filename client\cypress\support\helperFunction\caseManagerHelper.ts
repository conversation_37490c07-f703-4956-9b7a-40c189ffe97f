export const enum DataTestSelector {
  AddIconBtn = 'AddIcon',
  CreateNewCaseBtn = 'create-new-case-btn',
  UploadFileBtn = 'upload-files-btn',
  CreateCaseTitle = 'create-case-title',
  CaseNameTextField = 'case-name-textfield',
  CaseIdTextField = 'case-id-textfield',
  CaseDescriptionTextField = 'case-description-textfield',
  StatusList = 'case-status-list',
  TagList = 'multi-select-search-select',
  NumberOfTagsSelected = 'multi-select-number-selected',
  SearchTagInput = 'multi-select-search-input',
  DateSelectorForm = 'date-selector-form',
  SearchTableMenu = 'search-table-menu',
  ConfirmBtn = 'confirm-button',
  FilterBtn = 'table-header__filter-list-icon',
  FilterCaseNameInput = 'case-filter-caseName-textfield',
  FilterCaseIdInput = 'case-filter-caseId-textfield',
  CaseFilter = 'case-filter',
  CaseStatusFilter = 'case-status-filter-select',
  CaseTagFilter = 'case-tags-filter',
  CaseStatusMenu = 'case-status-menu',
  CaseDetailContent = 'case-details-content',
  ConfirmationInput = 'type-confirmation-input',
}

export const enum Graphql {
  GraphqlURL = '/v3/graphql',
  FetchCaseStatuses = `\n    query getLatestSDO {\n      structuredDataObjects(\n        schemaId: \"1ef079a4-2f66-4946-955d-ce3665af0c9d\"\n        limit:1\n        offset:0\n        orderBy: [{\n          field: modifiedDateTime,\n          direction: desc\n        }]) {\n        records {\n          id\n          data\n        }\n      }\n    }`,
  FetchTags = `\n    query getLatestSDO {\n      structuredDataObjects(\n        schemaId: \"a30fa449-aeb7-4945-95de-b47130f1d9ed\"\n        limit:1\n        offset:0\n        orderBy: [{\n          field: modifiedDateTime,\n          direction: desc\n        }]) {\n        records {\n          id\n          data\n        }\n      }\n    }`,
  FetchDetailPopup = `\n      query getFolder($folderId: ID!) {\n        folder(id: $folderId) {\n          id\n          name\n          treeObjectId\n          createdDateTime\n          modifiedDateTime\n          contentTemplates {\n            id\n            sdo {\n              id\n              data\n            }\n          }\n        }\n      }`,
  FetchCases = `query searchMedia($search: JSONData!) {\n      searchMedia(search: $search) {\n        jsondata\n      }\n    }`,
  DeleteCase = `\n      mutation createStructuredData($schemaId: ID!, $id: ID, $data: JSONData) {\n        createStructuredData(input: {schemaId: $schemaId, id: $id, data: $data }) {\n          id\n          data\n        }\n      }`,
  getChildFoldersByParentFolderId = `query getChildFoldersByParentFolderId ($id: ID!, $limit: Int, $offset: Int) { folder(id: $id) { childFolders(limit: $limit, offset: $offset, orderBy: { field: name, direction: asc }) { count records { id name modifiedDateTime treeObjectId contentTemplates { id sdo { id schemaId } } } } } }`,
  deleteFolder = `mutation deleteFolder($folderId: ID!) { deleteFolder(input: {id: $folderId, orderIndex: 0}) { id } }`,
  deleteSDO = `mutation deleteSDO($sdoId: ID!, $schemaId: ID!) { deleteStructuredData(input: { id: $sdoId, schemaId: $schemaId }) { id } }`,
}

export const dynamicGraphqlQuery = {
  DeleteCase: (id: string) => `
    query getSDO {
      structuredDataObject(
        schemaId: "199afc28-bdc9-4439-b547-d8187e83ac4a"
        id: "${id}"
      ) {
        schemaId
        id
        data
      }
    }
  `,
};

interface SDO {
  id: string;
  schemaId: string;
}

interface ContentTemplate {
  id: string;
  sdo?: SDO;
}

interface FolderRecord {
  id: string;
  name: string;
  modifiedDateTime: string;
  treeObjectId: string;
  contentTemplates?: ContentTemplate[];
}

export function deleteSDOsFromRecord(
  record: FolderRecord,
  targetFolderName: string
) {
  cy.log(
    `Checking contentTemplates for folder: ${targetFolderName}`,
    record.contentTemplates
  );

  if (record.contentTemplates && record.contentTemplates.length > 0) {
    cy.log(
      `Found ${record.contentTemplates.length} contentTemplate(s) for folder: ${targetFolderName}`
    );

    for (const contentTemplate of record.contentTemplates) {
      if (contentTemplate.sdo) {
        const sdoId = contentTemplate.sdo.id;
        const schemaId = contentTemplate.sdo.schemaId;

        const deleteSDOVariables = {
          sdoId: sdoId,
          schemaId: schemaId,
        };

        cy.log(
          `Deleting SDO for folder: ${targetFolderName}, SDO ID: ${sdoId}`
        );
        cy.Graphql(Graphql.deleteSDO, deleteSDOVariables).then(
          (deleteSDORes) => {
            if (deleteSDORes.body?.data?.deleteStructuredData?.id) {
              cy.log(`Successfully deleted SDO: ${sdoId}`);
            } else {
              cy.log(`Failed to delete SDO: ${sdoId}`, deleteSDORes.body);
            }
          }
        );
      } else {
        cy.log(
          `ContentTemplate has no SDO for folder: ${targetFolderName}`,
          contentTemplate
        );
      }
    }
  } else {
    cy.log(`No contentTemplates found for folder: ${targetFolderName}`);
  }
}

export function deleteFolderRecord(
  record: FolderRecord,
  targetFolderName: string
) {
  const deleteFolderVariables = {
    folderId: record.id,
  };

  cy.log(`Deleting folder: ${targetFolderName} with ID: ${record.id}`);
  cy.Graphql(Graphql.deleteFolder, deleteFolderVariables).then(
    (deleteFolderRes) => {
      if (deleteFolderRes.body?.data?.deleteFolder?.id) {
        cy.log(`Successfully deleted folder: ${targetFolderName}`);
      } else {
        cy.log(
          `Failed to delete folder: ${targetFolderName}`,
          deleteFolderRes.body
        );
      }
    }
  );
}
