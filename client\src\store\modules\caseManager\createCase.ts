import { CaseSDO, InvestigateCase } from '@shared-types/types';
import { GQLApi } from '@utils/helpers';

export async function createCase({
  investigateCase,
  rootFolderId,
  folderContentTemplateSchemaId,
  userId,
  userName,
  gql,
}: {
  investigateCase: InvestigateCase;
  rootFolderId: string;
  folderContentTemplateSchemaId: string;
  userId: string;
  userName: string;
  gql: GQLApi;
}) {
  let folderId = undefined;
  let sdoId = undefined;
  let contentTemplateId = undefined;
  const {
    caseName,
    description,
    createdBy,
    caseId,
    caseDate,
    preconfiguredTagIds,
    statusId,
    modifiedBy,
  } = investigateCase;

  try {
    folderId = await gql.createFolder({
      name: caseName,
      description,
      userId: createdBy || userId,
      parentFolderId: rootFolderId,
    });
    const now = new Date();
    const nowStr = now.toISOString();
    const data: CaseSDO = {
      caseName,
      caseId,
      description,
      caseDate,
      preconfiguredTagIds,
      statusId,
      folderId,
      createdBy: createdBy || userId,
      createdByName: userName,
      modifiedBy: modifiedBy || userId,
      createdDateTime: nowStr,
      modifiedDateTime: nowStr,
    };
    sdoId = (
      await gql.createSDO({
        schemaId: folderContentTemplateSchemaId,
        data,
      })
    ).id;
    contentTemplateId = await gql.createFolderContentTemplate({
      folderId,
      sdoId,
      schemaId: folderContentTemplateSchemaId,
    });
  } catch (error) {
    if (contentTemplateId) {
      try {
        // TODO: This function doesn't exist in the GQLApi yet, should it, or is this code completely unused and should be removed
        // await gql.deleteFolderContentTemplate(contentTemplateId);
      } catch (err) {
        console.error(
          `failed to clean up folder content template ${contentTemplateId} after create case failure`,
          err
        );
      }
    }
    if (sdoId) {
      try {
        await gql.deleteSDO({
          schemaId: folderContentTemplateSchemaId,
          id: sdoId,
        });
      } catch (err) {
        console.error(
          `failed to clean up sdo - schemaId ${folderContentTemplateSchemaId} sdo ${sdoId} after create case failure`,
          err
        );
      }
    }
    if (folderId) {
      try {
        await gql.deleteFolder(folderId);
      } catch (err) {
        console.error(
          `failed to clean up folder ${folderId} after create case failure`,
          err
        );
      }
    }
    throw error;
  }
  return folderId;
}
