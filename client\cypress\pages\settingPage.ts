import { CaseStatus } from '../../src/types/types';
import {
  ElementText,
  SettingDatatTestSelector,
  SettingsGraphql,
  SettingsType,
} from '../support/helperFunction/settingsHelper';

export const settingPage = {
  visit: (): void => {
    cy.visit('/settings');
    cy.get('body').should(($body) => {
      expect($body.text()).to.match(/Settings/);
    });
    cy.url().should('include', 'settings');
  },

  clickAddNewButton: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.AddNewButton })
      .should('be.visible')
      .click(),

  errorMessageIsShown: () =>
    cy
      .getByRoles(SettingDatatTestSelector.DialogRole)
      .contains(ElementText.ErrorMessage)
      .should('be.visible'),

  checkAddNewModal: (type: string) => {
    cy.getDataIdCy({ idAlias: 'status-dialog-title' }).should('be.visible');

    cy.getDataIdCy({ idAlias: `${type}-name-input` })
      .should('be.visible')
      .should('have.value', '');

    cy.getDataIdCy({ idAlias: 'confirm-button' })
      .should('be.visible')
      .should('be.disabled');
  },

  selectColor: (color: string) => {
    cy.get(SettingDatatTestSelector.DialogColorPicker).click();
    cy.get(`[style="background: ${color};"]`).click();
    cy.get('body').type('{esc}');
  },

  checkDialogIsOpen: () => {
    cy.getByRoles(SettingDatatTestSelector.DialogRole).should('be.visible');
  },

  typeInNameField: (type: string, statusName: string) => {
    const input =
      type === 'status'
        ? SettingDatatTestSelector.InputStatus
        : SettingDatatTestSelector.InputTag;
    cy.getDataIdCy({ idAlias: input }).should('be.visible');
    cy.getDataIdCy({ idAlias: input }).clear();
    cy.getDataIdCy({ idAlias: input }).type(statusName);
    cy.getDataIdCy({ idAlias: input }).should('have.value', statusName);
  },

  clickConfirmButton: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.ConfirmBtn })
      .should('be.visible')
      .and('not.be.disabled')
      .click(),

  checkNewPosition: (
    statusName: string,
    type: SettingsType,
    rowIndex: number
  ) => {
    const columnIndex = type === SettingsType.Status ? 2 : 1;
    cy.get('tbody tr')
      .eq(rowIndex - 1)
      .find('td')
      .eq(columnIndex)
      .contains(statusName)
      .should('be.visible');
  },

  checkSavedNotification: (Notification: string) => {
    cy.get(SettingDatatTestSelector.Notification).should(
      'contain.text',
      Notification
    );
    cy.get(SettingDatatTestSelector.Notification, { timeout: 120000 }).should(
      'not.exist'
    );
  },

  createDefaultStatus: (StatusName: string) => {
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.AddNewButton }).click();
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.InputStatus }).should(
      'be.visible'
    );
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.InputStatus }).clear();
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.InputStatus }).type(
      StatusName
    );
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.ConfirmBtn })
      .should('be.visible')
      .and('not.be.disabled')
      .click();
    cy.get(SettingDatatTestSelector.FirstRow)
      .children()
      .eq(2)
      .contains(StatusName);
    cy.get(SettingDatatTestSelector.Notification)
      .contains(ElementText.AddedStatusMessage)
      .should('be.visible');
  },

  clickEditButton: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.EditButton })
      .should('be.visible')
      .click(),

  scrollToRight: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.SettingContent })
      .then(($el) => {
        const hasHorizontalScroll = $el[0].scrollWidth > $el[0].clientWidth;
        if (hasHorizontalScroll) {
          $el[0].scrollLeft = $el[0].scrollWidth;
          expect($el[0].scrollLeft).to.equal(
            $el[0].scrollWidth - $el[0].clientWidth
          );
        }
      }),

  checkSaveChangesButtonDisabled: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.EditButton })
      .should('be.visible')
      .and('be.disabled'),

  selectColorIcon: () =>
    cy.get(SettingDatatTestSelector.FirstRow).within(() => {
      cy.getDataIdCy({
        idAlias: SettingDatatTestSelector.ColorPicker,
      })
        .should('be.visible')
        .click();
    }),

  checkSaveChangesButtonEnabled: (type: string, expectedStatusName: string) => {
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.EditButton })
      .should('be.visible')
      .and('not.be.disabled');

    let columnIndex;
    switch (type) {
      case 'status':
        columnIndex = 3;
        break;
      case 'tag':
        columnIndex = 2;
        break;
      default:
        throw new Error(
          `Unsupported type: ${type}. Expected 'status', 'tag', or other defined types.`
        );
    }

    cy.get(SettingDatatTestSelector.FirstRow)
      .children()
      .eq(columnIndex)
      .find('input')
      .should('have.value', expectedStatusName);
  },

  clickVisibilityBtn: () =>
    cy.get(SettingDatatTestSelector.FirstRow).within(() => {
      cy.getDataIdCy({ idAlias: SettingDatatTestSelector.VisibilityDropdown })
        .should('be.visible')
        .click();
    }),

  selectVisibilityOption: (option: string) =>
    cy.get('ul').within(() => {
      cy.contains('li', option).should('be.visible').click();
    }),
  checkUpdatedVisibility: (
    type: string,
    targetRowName: string,
    UpdatedVisibility: string
  ) =>
    cy.contains('tr', targetRowName).within(() => {
      const columnIndex = type === 'status' ? 3 : type === 'tag' ? 2 : -1;

      if (columnIndex !== -1) {
        cy.get('td').eq(columnIndex).should('contain.text', UpdatedVisibility);
      } else {
        throw new Error(
          `Unsupported type: ${type}. Expected 'status' or 'tag'.`
        );
      }
    }),

  checkUpdatedVisibilityInEdit: (
    UpdatedVisibility: string,
    targetRowName: string
  ) =>
    cy
      .get('tr')
      .filter(`:has(input[value="${targetRowName}"])`)
      .within(() => {
        cy.getDataIdCy({ idAlias: 'reorder-table-visibility-edit' }).should(
          'contain.text',
          UpdatedVisibility
        );
      }),

  selectColorFromTheColorScale: () => {
    cy.get(SettingDatatTestSelector.SaturationPointer).trigger('mousedown', {
      button: 0,
      clientX: 50,
    });
    cy.get(SettingDatatTestSelector.SaturationPointer).trigger('mousemove', {
      clientX: 200,
    });
    cy.get(SettingDatatTestSelector.SaturationPointer).trigger('mouseup');
  },

  inputHEXColor: (color: string) => {
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.HexPointer }).should(
      'be.visible'
    );
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.HexPointer }).clear();
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.HexPointer }).type(
      color
    );
  },

  clickAwayToCloseDialog: () => cy.get('body').click(0, 0),

  checkUpdatedColor: (color: string) => {
    const hexToRgb = (hex: string): string => {
      const bigint = parseInt(hex.slice(1), 16);
      const r = (bigint >> 16) & 255;
      const g = (bigint >> 8) & 255;
      const b = bigint & 255;
      return `rgb(${r}, ${g}, ${b})`;
    };

    const rgbColor = hexToRgb(color);
    cy.get(SettingDatatTestSelector.FirstRow).within(() => {
      cy.getDataIdCy({ idAlias: SettingDatatTestSelector.ColorSwatch }).should(
        'have.css',
        'background-color',
        rgbColor
      );
    });
  },

  editStatusName: (statusName: string) => {
    cy.get(SettingDatatTestSelector.FirstRow).within(() => {
      cy.getDataIdCy({ idAlias: SettingDatatTestSelector.NameReorder })
        .find('input')
        .as('inputField');
      cy.get('@inputField').should('be.visible').clear();
      cy.get('@inputField').type(statusName);
      cy.get('@inputField').should('have.value', statusName);
    });
  },
  checkUpdatedStatusName: (statusName: string, type: string) => {
    const columnIndex = type === 'status' ? 2 : 1;
    cy.get(SettingDatatTestSelector.FirstRow)
      .children()
      .eq(columnIndex)
      .contains(statusName)
      .should('be.visible');
  },

  clickDeleteButton: () =>
    cy.get(SettingDatatTestSelector.FirstRow).within(() => {
      cy.getDataIdCy({ idAlias: SettingDatatTestSelector.DeleteBtn })
        .should('be.visible')
        .click();
    }),

  checkRowDeletedOpacity: () =>
    cy
      .get(SettingDatatTestSelector.FirstRow)
      .should('have.css', 'opacity', '0.5'),

  clickCancelButton: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.CancelBtn })
      .should('be.visible')
      .click(),

  checkRowNotDeleted: () =>
    cy
      .get(SettingDatatTestSelector.FirstRow)
      .should('not.have.css', 'opacity', '0.5'),

  checkRowIsDeleted: (DeletedStatusName: string) => {
    cy.get(SettingDatatTestSelector.FirstRow)
      .children()
      .eq(3)
      .contains(DeletedStatusName)
      .should('not.exist');

    cy.Graphql(SettingsGraphql.FetchSettingStatuses, {}).then((res) => {
      const records = res.body?.data?.structuredDataObjects?.records || [];
      const statusExists = records.some(
        (record: { data: { statuses: CaseStatus[] } }) =>
          record.data.statuses.some(
            (status) => status.label === DeletedStatusName
          )
      );

      cy.wrap(statusExists).should('be.false');
    });
  },

  selectMultipleStatuses: (numberOfRows: number) => {
    cy.get('tbody').scrollTo('top', { ensureScrollable: false });

    for (let i = 0; i < numberOfRows; i++) {
      cy.get('tbody tr')
        .eq(i)
        .within(() => {
          cy.getDataIdCy({ idAlias: SettingDatatTestSelector.CheckBox })
            .should('be.visible')
            .and('not.be.checked')
            .click();
        });
    }
  },

  clickSetColorButton: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.ChangeColorBtn })
      .should('be.visible')
      .click(),

  clickSetVisibilityButton: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.ChangeVisibilityBtn })
      .should('be.visible')
      .click(),

  selectInactiveVisibility: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.InactiveButton })
      .should('be.visible')
      .and('not.be.checked')
      .click(),

  selectActiveVisibility: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.ActiveButton })
      .should('be.visible')
      .and('not.be.checked')
      .click(),

  clickDeleteStatusButton: () =>
    cy
      .getDataIdCy({ idAlias: SettingDatatTestSelector.DeleteStatusBtn })
      .should('be.visible')
      .click(),

  goToTagSetting: () => {
    cy.getDataIdCy({ idAlias: 'preconfigured-tags-tab' }).click();
    cy.get('.settings__option-header').should(
      'contain.text',
      'Pre-Configured Tag Settings'
    );
  },

  typeInTagNameField: (tagName: string) => {
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.InputTag }).clear();
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.InputTag }).type(
      tagName
    );
    cy.getDataIdCy({ idAlias: SettingDatatTestSelector.InputTag }).should(
      'have.value',
      tagName
    );
  },

  createDefaultTag: (tagName: string) => {
    settingPage.typeInTagNameField(tagName);
    settingPage.clickConfirmButton();
  },
};
