name: Build

on:
  workflow_dispatch:
  push:
    branches:
      - 'master'
      - 'hotfix/**'

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }} # TODO: Move back to GITHUB_TOKEN

jobs:
  build-and-deploy:
    runs-on: veritone-self-hosted-16gb
    if: |
      github.head_ref != 'prod' &&
      github.event_name != 'pull_request'
    steps:
      - name: Install Git
        run: |
          sudo apt-get update
          sudo apt-get install -y git
      - name: Fix Workspace Permissions
        run: sudo chown -R $USER:$USER ${{ github.workspace }}
      - name: Checkout Code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Setup /opt/hostedtoolcache
        run: |
          sudo mkdir -p -m 777 /opt/hostedtoolcache
          export AGENT_TOOLSDIRECTORY=/opt/hostedtoolcache
      - name: Setup Python3.x
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '22.x'
          always-auth: true
          cache: 'npm'
          registry-url: https://registry.npmjs.org
        env:
          PERCY_ENABLE: 0
      - name: Install Corepack
        run: npm install -g corepack@latest
      - name: Enable Corepack
        run: corepack enable
      - name: Install Yarn
        run: corepack prepare yarn@4.6.0 --activate
      - name: Fix Cache Permissions
        run: sudo rm -rf /home/<USER>/.cache
      - name: increase the memory heap
        run: export NODE_OPTIONS="--max-old-space-size=15288"
      - run: yarn install --immutable
      - name: Setup kernel (increase watches)
        run: echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p
      - name: Increase Memory Heap
        run: export NODE_OPTIONS="--max-old-space-size=8192"
      - name: Get current date
        id: current-time
        run: echo "::set-output name=current-time::$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
      - name: Create build.json
        run: |
          echo '{
            "git_commit": "${{ github.sha }}",
            "build_date": "${{ steps.current-time.outputs.current-time }}"
          }' > ./client/build.json

      - name: Restore cache of sdk build
        uses: actions/cache/restore@v3
        id: restore-previous-cache
        with:
          key: ${{ runner.os }}-build-${{ github.sha }}-${{ github.run_id }}-${{ github.run_attempt }}
          path: |
            dist
            node_modules
          restore-keys: |
            ${{ runner.os }}-build-${{ github.sha }}-
            ${{ runner.os }}-build-${{ github.event.before }}-
            ${{ runner.os }}-build-${{ env.NX_BASE }}-
            ${{ runner.os }}-build-
      - name: Build Investigate Application
        id: build
        run: |
          git fetch --no-tags --prune --depth=100 origin master
          yarn install --immutable
          yarn vite build --mode development
        working-directory: client
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            investigate-app
          tags: |
            type=sha,prefix=,format=long
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Login to Registry Central
        uses: docker/login-action@v2
        with:
          registry: registry.central.aiware.com
          username: ${{secrets.REGISTRY_CENTRAL_USERNAME}}
          password: ${{secrets.REGISTRY_CENTRAL_PASSWORD}}
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver: docker
      - name: Build Investigate Image
        run: |
          docker build \
          --build-arg APPLICATION=investigate-app \
          --build-arg GIT_COMMIT=${{ github.sha }} \
          --build-arg BUILD_DATE=${{ steps.current-time.outputs.current-time }} \
          --build-arg GITHUB_ACCESS_TOKEN=${{ secrets.ACCESS_TOKEN }} \
          --tag registry.central.aiware.com/investigate-app:${{ github.sha }} \
          --file ./Dockerfile .
      - name: Push Investigate Image
        run: docker push registry.central.aiware.com/investigate-app:${{ github.sha }}
