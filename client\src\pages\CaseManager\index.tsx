import './index.scss';
import CaseTable from '@components/CaseManager/CaseTable';
import { DataMap } from '@components/Table';
import { I18nTranslate } from '@i18n';
import { CaseSearchResults } from '@shared-types/types';
import { useAppDispatch } from '@store/hooks';
import {
  setCurrentCaseId,
  syncPendingFiles,
} from '@store/modules/caseDetail/slice';
import {
  checkCase,
  fileUploadedSuccessfully,
  selectCaseData,
  selectCases,
  selectCaseValidityStatus,
  selectInvalidCases,
  selectValidCases,
} from '@store/modules/caseManager/slice';
import {
  selectConfig,
  selectEvidenceTypeSchema,
  selectStatusSchema,
  selectTagSchema,
} from '@store/modules/config/slice';
import {
  fetchMetadata,
  selectMetadataByFolderId,
} from '@store/modules/metadata/slice';
import { fetchStatuses, fetchTags } from '@store/modules/settings/slice';
import { uploadFile } from '@utils/files';
import { updatePendingDeleteCaseToLocalStorage } from '@utils/saveToLocalStorage';
import { debounce } from 'lodash';
import { useSnackbar } from 'notistack';
import { SetStateAction, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import CaseDetails from './CaseDetails';

type Case = CaseSearchResults['searchMedia']['jsondata']['results'][0];

const DEBOUNCE_WAIT_MS = 350;
const debouncedSelectFolder = debounce(
  (
    callback: (value: SetStateAction<string | undefined>) => void,
    id: string
  ) => {
    callback(id);
  },
  DEBOUNCE_WAIT_MS
);

const CaseManager = () => {
  const navigate = useNavigate();
  const intl = I18nTranslate.Intl();

  const [selected, setSelected] = useState('');
  const [selectedFolderId, setSelectedFolderId] = useState<
    string | undefined
  >();
  const [showCaseFiles, setShowCaseFiles] = useState(false);
  const [showCaseClick, setShowCaseClick] = useState(false);
  const selectedCase = useSelector(selectCaseData);

  const dispatch = useAppDispatch();
  const {
    data: { results: cases, totalResults },
  } = useSelector(selectCases);
  const tagSchemaId = useSelector(selectTagSchema).id;
  const { registryIds } = useSelector(selectConfig);

  const statusSchemaId = useSelector(selectStatusSchema).id;
  const evidenceTypeSchemaId = useSelector(selectEvidenceTypeSchema).id;
  const metadataByFolderId = useSelector(selectMetadataByFolderId);

  const validCaseIds = useSelector(selectValidCases);
  const invalidCaseIds = useSelector(selectInvalidCases);
  const caseValidityStatus = useSelector(selectCaseValidityStatus);

  const { enqueueSnackbar } = useSnackbar();
  const [pendingDeleteCaseIds, setPendingDeleteCaseIds] = useState<string[]>(
    []
  );

  const caseMap = useMemo(
    () =>
      cases.reduce((acc: DataMap<Case>, item, index) => {
        acc[item.id] = { index, item };
        return acc;
      }, {}),
    [cases]
  );

  const handleUploadFile = () => {
    uploadFile({
      selectedFolderId,
      selectedCaseName: selectedCase?.caseName,
      registryId: registryIds?.caseRegistryId,
      intl,
    });
  };

  // if aiware can return folderId in the data response, I think we should move this effect to the layout to handle upload files for entire app
  useEffect(() => {
    if (selectedFolderId && window.aiware) {
      window.aiware.on('tdoCreated', (error, data) => {
        if (error) {
          enqueueSnackbar(intl.formatMessage({ id: 'unableToUploadFile' }), {
            variant: 'error',
          });
        } else {
          const tdoData = data as { tdoId: string; fileId: string };
          // TODO: handle save file to local storage while data from aiware is ready
          // dispatch(syncPendingFiles({ folderId: selectedFolderId, file: {
          //   id: tdoData.tdoId,
          //   fileName: tdoData.fileName,
          //   createdTime: tdoData.createdTime,
          //   fileType: tdoData.fileType,
          // } }));
          dispatch(syncPendingFiles({ folderId: selectedFolderId }));
          dispatch(fileUploadedSuccessfully({ tdoId: tdoData.tdoId }));
        }
      });
    }

    return () => {
      window.aiware?.off?.('tdoCreated');
    };
  }, [selectedFolderId]);

  useEffect(() => {
    try {
      const validCaseSdoIds = updatePendingDeleteCaseToLocalStorage();
      setPendingDeleteCaseIds(validCaseSdoIds.map((item) => item.value));
    } catch (e) {
      console.error('unable to update deleted case to local storage', e);
    }
  }, [cases]);

  useEffect(() => {
    if (
      selectedFolderId &&
      (metadataByFolderId?.[selectedFolderId] === undefined ||
        metadataByFolderId[selectedFolderId]?.status === 'idle')
    ) {
      dispatch(
        fetchMetadata({ folderId: selectedFolderId, evidenceTypeSchemaId })
      );
    }
  }, [cases, selectedFolderId]);

  useEffect(() => {
    dispatch(fetchTags({}));
  }, [tagSchemaId]);

  useEffect(() => {
    dispatch(fetchStatuses({}));
  }, [statusSchemaId]);

  const handleSelect = (selectedId: string) => {
    if (!caseMap[selectedId]) {
      return;
    }

    setSelected(selectedId);
    const selectedCase = caseMap[selectedId].item;
    debouncedSelectFolder(setSelectedFolderId, selectedCase.folderId);
  };

  useEffect(() => {
    if (
      showCaseClick &&
      caseValidityStatus === 'complete' &&
      selectedFolderId
    ) {
      if (validCaseIds.includes(selectedFolderId)) {
        viewCaseDetails();
      } else {
        enqueueSnackbar(intl.formatMessage({ id: 'invalidCase' }), {
          variant: 'error',
        });
      }
      setShowCaseClick(false);
    }
    if (showCaseClick && caseValidityStatus === 'failure') {
      setShowCaseClick(false);
    }
  }, [showCaseClick, caseValidityStatus]);

  const viewCaseDetails = () => {
    if (!selectedFolderId) {
      return;
    }

    setShowCaseFiles(true);
    navigate(`/case-manager/${selectedFolderId}`);

    const currentCase = cases.find(
      (item) => item.folderId === selectedFolderId
    );
    if (currentCase) {
      dispatch(setCurrentCaseId(currentCase.id));
    }
  };

  const handleDoubleClick = () => {
    if (selectedFolderId) {
      if (invalidCaseIds.includes(selectedFolderId)) {
        enqueueSnackbar(intl.formatMessage({ id: 'invalidCase' }), {
          variant: 'error',
        });
      } else {
        dispatch(checkCase(selectedFolderId));
        setShowCaseClick(true);
      }
    }
  };

  return (
    <div className="case-manager">
      <div className="case-manager__header">
        <>
          <div className="case-manager__title">
            {I18nTranslate.TranslateMessage('caseManagement')}
          </div>
          <div className="case-manager__count">{totalResults}</div>
        </>
      </div>
      <div className="case-manager__content">
        <CaseTable
          caseMap={caseMap}
          selected={selected}
          handleSelect={handleSelect}
          handleDoubleClick={handleDoubleClick}
          pendingDeleteIds={pendingDeleteCaseIds}
          handleUploadFile={handleUploadFile}
          setPendingDeleteIds={setPendingDeleteCaseIds}
          setSelected={setSelected}
          classname="case-manager__table"
        />
        <CaseDetails
          folderId={selectedFolderId}
          showCaseFiles={showCaseFiles}
          onShowCaseFiles={viewCaseDetails}
        />
      </div>
    </div>
  );
};

export default CaseManager;
