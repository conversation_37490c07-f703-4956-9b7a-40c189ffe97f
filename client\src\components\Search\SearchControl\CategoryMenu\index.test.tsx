import { fireEvent, screen, within } from '@testing-library/dom';
import {
  defaultCategories,
  getCategoriesLocalStorage,
} from '@utils/local-storage/categories';
import { getHideIfNoResultsLocalStorage } from '@utils/local-storage/hideIfNoResults';
import { SearchView } from '@utils/local-storage/searchView';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CategoryMenu from '.';
import { render } from '../../../../../test/render';

describe('categories menu', () => {
  beforeEach(() => {
    const localStorageMock = (function () {
      let store: Record<string, string> = {};

      return {
        getItem: vi.fn((key: string) => store[key] || null),
        setItem: vi.fn((key: string, value: string) => {
          store[key] = value;
        }),
        removeItem: vi.fn((key: string) => {
          delete store[key];
        }),
        clear: vi.fn(() => {
          store = {};
        }),
      };
    })();

    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });
  });

  it('should show all of default categories', () => {
    render(
      <CategoryMenu
        open
        onClose={vi.fn()}
        anchorEl={null}
        searchView={SearchView.Grouped}
      />
    );
    const categoryList = [
      'Transcription',
      'Facial Recognition',
      'Object Detection',
      'Vehicle Recognition',
      'License Plate Recognition',
      // 'Scene Classification',
      'Text Recognition',
      'Metadata',
    ];
    categoryList.forEach((category) => {
      expect(screen.getByText(category)).toBeInTheDocument();
    });
  });

  it('should save to local storage when drag and drop', () => {
    render(
      <CategoryMenu
        open
        onClose={vi.fn()}
        anchorEl={null}
        searchView={SearchView.Grouped}
      />
    );

    const localStorageCategories = getCategoriesLocalStorage();
    expect(localStorageCategories[1].category).toBe(
      defaultCategories[1].category
    );
    expect(localStorageCategories[1].category).toBe(
      defaultCategories[1].category
    );

    const transcriptionDragIcon = within(
      screen.getByRole('row', {
        name: /toggle select row transcription/i,
      })
    ).getByRole('button', { name: /move/i });
    const faceRecognitionDragIcon = within(
      screen.getByRole('row', {
        name: /toggle select row facial recognition/i,
      })
    ).getByRole('button', { name: /move/i });

    fireEvent.dragStart(transcriptionDragIcon, {
      dataTransfer: {
        setData: vi.fn(),
        getData: vi.fn(),
        setDragImage: vi.fn(),
      },
    });
    fireEvent.dragEnter(faceRecognitionDragIcon);
    fireEvent.dragOver(faceRecognitionDragIcon);
    fireEvent.drop(faceRecognitionDragIcon, {
      dataTransfer: {
        getData: vi.fn().mockReturnValue('some-data'),
      },
    });
    fireEvent.dragEnd(transcriptionDragIcon);

    const newLocalStorageCategories = getCategoriesLocalStorage();
    expect(newLocalStorageCategories[1].category).toBe(
      defaultCategories[2].category
    );
    expect(newLocalStorageCategories[1].category).toBe(
      defaultCategories[2].category
    );
  });

  it('should save to local storage when select checkbox', () => {
    render(
      <CategoryMenu
        open
        onClose={vi.fn()}
        anchorEl={null}
        searchView={SearchView.Grouped}
      />
    );

    const localStorageCategories = getCategoriesLocalStorage();
    expect(localStorageCategories[1].category).toBe(
      defaultCategories[1].category
    );
    expect(!!localStorageCategories[1]?.checked).toBe(true);

    const transcriptionCheckbox = within(
      screen.getByRole('row', {
        name: /toggle select row transcription/i,
      })
    ).getByRole('checkbox', { name: /toggle select row/i });
    fireEvent.click(transcriptionCheckbox);

    const newLocalStorageCategories = getCategoriesLocalStorage();
    expect(!!newLocalStorageCategories[1]?.checked).toBe(false);
  });

  it('should save to local storage when toggle switch hide-if-no-results', () => {
    render(
      <CategoryMenu
        open
        onClose={vi.fn()}
        anchorEl={null}
        searchView={SearchView.Grouped}
      />
    );

    const hideIfNoResults = getHideIfNoResultsLocalStorage();
    expect(hideIfNoResults).toBe(false);

    const hideIfNoResultsSwitch = screen.getByTestId('hide-if-no-results');
    fireEvent.click(hideIfNoResultsSwitch);

    const newHideIfNoResults = getHideIfNoResultsLocalStorage();
    expect(newHideIfNoResults).toBe(true);
  });

  it('should be sortable in the Grouped View', () => {
    render(
      <CategoryMenu
        open
        onClose={vi.fn()}
        anchorEl={null}
        searchView={SearchView.Grouped}
      />
    );

    expect(screen.getByTestId('category-menu-table').innerHTML).not.toContain(
      'no-row-ordering'
    );
  });

  it('should show hide if no results in the Grouped View', () => {
    render(
      <CategoryMenu
        open
        onClose={vi.fn()}
        anchorEl={null}
        searchView={SearchView.Grouped}
      />
    );

    expect(screen.getByTestId('category-menu-action')).toBeInTheDocument();
  });
});
