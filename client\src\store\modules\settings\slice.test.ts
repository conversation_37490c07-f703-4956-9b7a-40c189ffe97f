import { I18nTranslate } from '@i18n';
import { CaseStatus } from '@shared-types/types';
import { configureAppStore } from '@store/index';
import {
  CaseManagerSliceState,
  initialState as caseManagerInitialState,
} from '@store/modules/caseManager/slice';
import { ConfigSliceState } from '@store/modules/config/slice';
import { GQLApi } from '@utils/helpers';
import { afterEach } from 'node:test';
import { describe, expect, it, vi } from 'vitest';
import {
  SettingsSliceState,
  saveStatuses,
  saveTags,
  initialState as settingsInitialState,
} from './slice';

vi.mock('../../../utils/helpers/gqlApi/baseGraphQLApi', () => ({
  baseGraphQLApiThrowError: vi.fn(),
  baseGraphQLApi: vi.fn(),
}));

vi.mock('../../../i18n', () => ({
  I18nTranslate: {
    TranslateMessage: vi.fn((key: string) => key),
  },
}));

const initialStateForMock: {
  settings: SettingsSliceState;
  caseManager: CaseManagerSliceState;
  appConfig: ConfigSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  settings: settingsInitialState,
  caseManager: {
    ...caseManagerInitialState,
    createCase: {
      status: 'idle',
      error: '',
      id: '',
    },
    deleteCase: {
      status: 'idle',
      error: '',
      id: '',
    },
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'rootFolder123',
    },
    folderContentTemplateSchema: {
      status: 'idle',
      error: '',
      id: '',
    },
  },
  appConfig: {
    statusSchema: {
      status: 'idle',
      error: '',
      id: 'statusSchemaId123',
    },
    tagSchema: {
      status: 'idle',
      error: '',
      id: 'tagSchemaId123',
    },
    evidenceTypeSchema: {
      status: 'idle',
      error: '',
      id: '',
    },
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId123',
      tagRegistryId: 'tagRegistryId123',
    },
    nodeEnv: 'dev',
  },
  auth: { sessionToken: 'sessionToken123' },
};

describe('caseManagerSlice', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should save statuses', async () => {
    vi.spyOn(GQLApi.prototype, 'createSDO').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'sdoId123',
        data: {
          statuses: [
            { id: 'id123', label: 'active', color: '#ffff', active: true },
          ],
          createdBy: 'userId',
        },
      })
    );
    const mockedStore = configureAppStore(initialStateForMock);

    const testStatuses = [
      { id: 'id123', label: 'active', color: '#ffff', active: true },
    ];

    // dispatch the saveStatuses thunk
    await mockedStore.dispatch(saveStatuses({ statuses: testStatuses }));

    // get the updated state
    const newState = mockedStore.getState();

    expect(newState.settings.saveStatuses.status).toBe('complete');
  });

  it('gets an error when statuses are modified by others', async () => {
    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'differentSdoId',
        data: [{ id: 'id123', label: 'status1', color: '#ffff', active: true }],
      })
    );

    const loadedSdoId = 'loadedSdoId';

    const mockedStore = configureAppStore(initialStateForMock);

    const testStatuses = [
      { id: 'id123', label: 'active', color: '#ffff', active: true },
    ] as CaseStatus[];

    // dispatch the saveStatuses thunk
    await mockedStore.dispatch(
      saveStatuses({ statuses: testStatuses, sdoId: loadedSdoId })
    );

    // get the updated state
    const newState = mockedStore.getState();

    expect(newState.settings.saveStatuses.status).toBe(
      'concurrentModificationError'
    );
  });

  it('should save tags', async () => {
    vi.spyOn(GQLApi.prototype, 'createSDO').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'newSdoId',
        data: {
          tags: [{ id: 'id123', label: 'active', active: true }],
          createdBy: 'userId',
        },
      })
    );
    const mockedStore = configureAppStore(initialStateForMock);

    const testPayload = {
      tags: [{ id: 'id123', label: 'active', active: true }],
    };

    // dispatch the saveTags thunk
    await mockedStore.dispatch(saveTags(testPayload));

    // get the updated state
    const newState = mockedStore.getState();

    expect(newState.settings.saveTags.status).toBe('complete');
  });

  it('get error when tags are modified by others', async () => {
    vi.spyOn(GQLApi.prototype, 'getLatestSDO').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'differentSdoId',
        data: [{ id: 'id123', label: 'tag1', active: true }],
      })
    );

    const mockedStore = configureAppStore(initialStateForMock);

    const testPayload = {
      sdoId: 'loadedSdoId',
      tags: [{ id: 'id123', label: 'active', active: true }],
    };

    // dispatch the saveTags thunk
    await mockedStore.dispatch(saveTags(testPayload));

    // get the updated state
    const newState = mockedStore.getState();

    expect(newState.settings.saveTags.status).toBe(
      'concurrentModificationError'
    );
  });

  it('should show error when create/save tags fails', async () => {
    vi.spyOn(GQLApi.prototype, 'createSDO').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'newSdoId',
        data: {
          tags: [{ id: 'id123', label: 'active', active: true }],
          createdBy: 'userId',
        },
      })
    );
    const mockedStore = configureAppStore({
      ...initialStateForMock,
      appConfig: {
        ...initialStateForMock.appConfig,
        tagSchema: {
          status: 'idle',
          error: '',
          id: '',
        },
      },
    });

    vi.spyOn(console, 'error').mockImplementation(() => {});
    // create tag fails
    const createPayload = {
      tags: [{ id: 'id123', label: 'active', active: true }],
      isAddNew: true,
    };
    await mockedStore.dispatch(saveTags(createPayload));
    const createdNewState = mockedStore.getState();
    expect(createdNewState.settings.saveTags.status).toBe('failure');
    expect(I18nTranslate.TranslateMessage).toHaveBeenCalledWith(
      'addTagFailure'
    );
    vi.restoreAllMocks();

    vi.spyOn(console, 'error').mockImplementation(() => {});
    // save tag fails
    const savePayload = {
      tags: [{ id: 'id123', label: 'active', active: true }],
    };
    await mockedStore.dispatch(saveTags(savePayload));
    const savedNewState = mockedStore.getState();
    expect(savedNewState.settings.saveTags.status).toBe('failure');
    expect(I18nTranslate.TranslateMessage).toHaveBeenCalledWith(
      'saveTagsFailure'
    );
    vi.restoreAllMocks();
  });

  it('should show error when create/save statuses fails', async () => {
    vi.spyOn(GQLApi.prototype, 'createSDO').mockImplementationOnce(() =>
      Promise.resolve({
        id: 'newSdoId',
        data: {
          tags: [{ id: 'id123', label: 'active', active: true }],
          createdBy: 'userId',
        },
      })
    );
    const mockedStore = configureAppStore({
      ...initialStateForMock,
      appConfig: {
        ...initialStateForMock.appConfig,
        statusSchema: {
          status: 'idle',
          error: '',
          id: '',
        },
      },
    });

    vi.spyOn(console, 'error').mockImplementation(() => {});
    // create tag fails
    const createPayload = {
      statuses: [{ id: 'id123', label: 'active', color: '#fff', active: true }],
      isAddNew: true,
    };
    await mockedStore.dispatch(saveStatuses(createPayload));
    const createdNewState = mockedStore.getState();
    expect(createdNewState.settings.saveStatuses.status).toBe('failure');
    expect(I18nTranslate.TranslateMessage).toHaveBeenCalledWith(
      'addStatusFailure'
    );
    vi.restoreAllMocks();

    vi.spyOn(console, 'error').mockImplementation(() => {});
    // save tag fails
    const savePayload = {
      statuses: [{ id: 'id123', label: 'active', color: '#fff', active: true }],
    };
    await mockedStore.dispatch(saveStatuses(savePayload));
    const savedNewState = mockedStore.getState();
    expect(savedNewState.settings.saveStatuses.status).toBe('failure');
    expect(I18nTranslate.TranslateMessage).toHaveBeenCalledWith(
      'saveStatusesFailure'
    );
    vi.restoreAllMocks();
  });
});
