## GraphQL Query: search files with Metadata fields

```graphql
query searchMedia($search: JSONData!) {
  searchMedia(search: $search) {
    jsondata {
      results [
       {
        cadId
        badgeId
        deviceId
        lastName
        firstName
        cameraType
        deviceName
        deviceType
        unitNumber
        citizenName
        dateOfBirth
        deviceModel
        interviewee
        interviewer
        officerName
        evidenceType
        locationTimeline
        callerPhoneNumber
        evidenceTechnician
        cameraFacingDirection
        cameraPhysicalAddress
        deviceRegisteredOwner
        cameraPhysicalLocation
      }
      ]
      totalResults
      limit
      from
      to
      searchToken
      timestamp
    }
  }
}
```

## $Search Variable

```{
  "search": {
    "index": ["mine"],
    "limit": 10,
    "offset": 0,
    "type":"e9b53bff-fadb-4e9d-96d4-b49890e4a058",
    "query": {
      "conditions": [
        {
          "field": "evidenceTypes",
          "operator": "terms",
          "values": [
             "911 Call Recording",
              "Arrest Report",
              "Body Worn Camera",
              "Booking Photo",
              "Citizen Submitted Video",
              "Crime Scene Photo",
              "In Car Video",
              "Interview Audio Recording",
              "Interview Room Recording",
              "Mobile Device Extraction",
              "Security Camera Video"
          ]
        },
        {
          "field": "cadId",
          "operator": "term",
          "value": "123456789"
        },
        {
          "field": "badgeId",
          "operator": "term",
          "value": "123456789"
        },
        {
          "field": "deviceId",
          "operator": "term",
          "value": "123456789"
        },
        {
          "field": "lastName",
          "operator": "term",
          "value": "last name"
        },
        {
          "field": "firstName",
          "operator": "term",
          "value": "first name"
        },
        {
          "field": "cameraType",
          "operator": "terms",
          "values": [
             "Doorbell Camera",
             "Mobile Phone",
             "Security Camera"
          ]
        },
        {
          "field": "deviceName",
          "operator": "term",
          "value": "device name"
        },
        {
          "field": "deviceType",
          "operator": "terms",
          "values": [
              "Desktop",
              "Laptop",
              "Phone",
              "Tablet",
              "Other (e.g. iPod Touch)"
          ]
        },
        {
          "field": "unitNumber",
          "operator": "query_string",
          "values": "*unit number*"
        },
        {
          "field": "citizenName",
          "operator": "term",
          "values": "*unit number*"
        },
        {
          "operator": "range",
          "field": "dateOfBirth",
          "gt": "2025-04-04T00:00:00.000Z",
          "lt": "2025-04-03T00:00:00.000Z"
          "not": true,
        },
        {
           "field": "deviceModel",
           "operator": "query_string",
           "value": "*device model example*"
        },
        {
           "field": "interviewee",
           "operator": "term",
           "value": "interviewee example"
        },
        {
           "field": "interviewer",
           "operator": "term",
           "value": "interviewer example"
        },
        {
           "field": "officerName",
           "operator": "query_string",
           "value": "*officer name*"
        },
        {
          "field": "reportNumber",
          "operator": "query_string",
          "value": "*report number*"
        },
        {
          "field": "interviewRoom",
          "operator": "term",
          "value": "interview room example"
        },
         {
          "field": "locationTimeline.latitude",
          "operator": "range",
          "gte": "40.0000",
          "lte": "41.0000"
        },
        {
          "field": "locationTimeline.longitude",
          "operator": "range",
          "gte": "-75.0000",
          "lte": "-73.0000"
        },
        {
          "field": "callerPhoneNumber",
          "operator": "query_string",
          "value": "*phone number*"
        },
        {
          "field": "evidenceTechnician",
          "operator": "term",
          "value": "evidence technician"
        },
        {
          "field": "cameraFacingDirection",
          "operator": "term",
          "value": "camera facing direction"
        },
        {
          "field": "deviceRegisteredOwner",
          "operator": "term",
          "value": "device registered owner"
        },
        {
          "field": "cameraPhysicalLocation",
          "operator": "term",
          "value": "camera physical location"
        }
      ],
      "operator": "and"
    }
  }
}
```
