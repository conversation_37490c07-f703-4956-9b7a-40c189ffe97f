import './index.scss';
import { ValueType } from '@components/EditMetadata/Field';
import { I18nTranslate } from '@i18n';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import { TextField } from '@mui/material';
import cn from 'classnames';
import { Controller, RegisterOptions, useFormContext } from 'react-hook-form';

interface Props {
  name: string;
  valueType: ValueType;
}

const INPUT_ROWS = 3;

const MetadataInput = ({ name, valueType }: Props) => {
  const intl = I18nTranslate.Intl();
  const { control } = useFormContext();

  // const pattern = {
  //   value: /^[a-zA-Z0-9\s_-]+$/,
  //   message: intl.formatMessage({ id: 'allowedLetters' }),
  // };

  const validation: Record<string, RegisterOptions> = {
    fileName: {
      required: intl.formatMessage({ id: 'fileNameRequired' }),
    },
    // caseName: {
    //   required: intl.formatMessage({ id: 'caseNameRequired' }),
    //   pattern,
    // },
    // caseId: {
    //   pattern,
    // },
  };

  return (
    <div className="edit-metadata-input">
      <Controller
        name={name}
        control={control}
        rules={validation[name]}
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            fullWidth
            multiline={valueType === ValueType.Textarea}
            rows={valueType === ValueType.Textarea ? INPUT_ROWS : 1}
            slotProps={{
              input: {
                className: cn('metadata-input', {
                  'normal-input': valueType === ValueType.Input,
                  'text-area-input': valueType === ValueType.Textarea,
                }),
              },
            }}
            error={!!fieldState.error}
            helperText={fieldState.error?.message}
          />
        )}
      />
      {valueType === ValueType.Textarea && (
        <ArrowDropUpIcon className="text-area-icon" />
      )}
    </div>
  );
};

export default MetadataInput;
