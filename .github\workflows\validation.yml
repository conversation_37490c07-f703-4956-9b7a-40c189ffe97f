name: Run Tests and Linter

on:
  pull_request:
    branches:
      - 'feature/**'
  push:
    branches:
      - 'master'
      - 'feature/**'

env:
  GITHUB_ACCESS_TOKEN: ${{ secrets.ACCESS_TOKEN }} # TODO: Move back to GITHUB_TOKEN

jobs:
  build:
    runs-on: ubuntu-latest
    env:
      NODE_OPTIONS: "--max_old_space_size=4096"
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '22.x'
      - name: install corepack
        run: npm install -g corepack@latest
      - name: enable corepack
        run: corepack enable
      - name: prepare yarn
        run: corepack prepare yarn@4.6.0 --activate
      - run: yarn
      - run: yarn
        working-directory: client
      - run: yarn lint
        working-directory: client
      - run: yarn lint:cypress
        working-directory: client
      - run: yarn test
        working-directory: client
