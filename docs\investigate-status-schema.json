{"type": "object", "title": "investigate-status", "required": ["key", "label", "color", "order", "active", "created<PERSON>y", "modifiedBy", "createdDateTime", "modifiedDateTime"], "properties": {"key": {"type": "string"}, "color": {"type": "string"}, "label": {"type": "string"}, "order": {"type": "integer"}, "active": {"type": "boolean"}, "createdBy": {"type": "string"}, "modifiedBy": {"type": "string"}, "createdDateTime": {"type": "dateTime"}, "modifiedDateTime": {"type": "dateTime"}}, "description": "investigate status"}