import { ResultCategory } from '@store/modules/search/slice';
import { isEqual } from 'lodash';

const CATEGORY_KEY = 'investigate-categories';

export interface Category {
  id: string;
  category: ResultCategory;
  color: string;
  checked?: boolean;
  expanded?: boolean;
}

export const defaultCategories: Category[] = [
  {
    id: 'filename',
    category: 'filename',
    color: 'var(--background-filename)',
    expanded: true,
    checked: true,
  },
  {
    id: 'transcription',
    category: 'transcription',
    color: 'var(--background-transcription)',
    expanded: false,
    checked: true,
  },
  {
    id: 'face-recognition',
    category: 'faceRecognition',
    color: 'var(--background-face-recognition)',
    expanded: false,
    checked: true,
  },
  {
    id: 'object-detection',
    category: 'objectDetection',
    color: 'var(--background-object-detection)',
    expanded: false,
    checked: true,
  },
  {
    id: 'vehicle-recognition',
    category: 'vehicleRecognition',
    color: 'var(--background-vehicle-recognition)',
    expanded: false,
    checked: true,
  },
  {
    id: 'license-plate-recognition',
    category: 'licensePlateRecognition',
    color: 'var(--background-license-plate-recognition)',
    expanded: false,
    checked: true,
  },
  // {
  //   id: 'scene-classification',
  //   category: 'sceneClassification',
  //   color: 'var(--background-scene-classification)',
  //   expanded: true,
  // },
  {
    id: 'text-recognition',
    category: 'textRecognition',
    color: 'var(--background-text-recognition)',
    expanded: false,
    checked: true,
  },
  {
    id: 'metadata',
    category: 'metadata',
    color: 'var(--background-metadata)',
    expanded: false,
    checked: true,
  },
];

export const isEqualCategories = (categories: Category[]): boolean => {
  const normalize = (arr: Category[]) =>
    arr
      .map(({ id, category }) => ({ id, category }))
      .sort((a, b) => a.id.localeCompare(b.id));

  return isEqual(normalize(defaultCategories), normalize(categories));
};

export const getCategoriesLocalStorage = (): Category[] => {
  try {
    const storage = localStorage.getItem(CATEGORY_KEY);
    if (!storage) {
      return defaultCategories;
    }

    // TODO: Type of parsed item should be validated
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const parsed: Category[] = JSON.parse(storage);

    return isEqualCategories(parsed) ? parsed : defaultCategories;
  } catch {
    return defaultCategories;
  }
};

export const setCategoriesLocalStorage = (categories: Category[]) => {
  localStorage.setItem(CATEGORY_KEY, JSON.stringify(categories));
};
