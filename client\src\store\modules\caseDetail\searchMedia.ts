import {
  FILES_SORT_DIRECTION,
  FILES_SORT_FIELD,
  VFile,
} from '@shared-types/types';
import { getDescriptions } from '@store/modules/search/getDescriptions';
import { GQLApi } from '@utils/helpers';
import { uniq } from 'lodash';

export async function searchMedia({
  rootFolderId,
  fileName,
  folderId,
  dateFilter,
  sortBy = 'createdTime',
  sortDirection = 'desc',
  offset,
  limit,
  gql,
}: {
  rootFolderId?: string;
  fileName?: string;
  folderId: string;
  dateFilter?: {
    startDate: string;
    endDate: string;
  };
  sortBy?: FILES_SORT_FIELD;
  sortDirection?: FILES_SORT_DIRECTION;
  offset: number;
  limit: number;
  gql: GQLApi;
}) {
  if (!rootFolderId) {
    throw new Error('no root folder');
  }

  const response = await gql.searchMedia({
    fileName,
    folderId,
    offset,
    limit,
    sortBy,
    sortDirection,
    dateFilter,
  });

  const { totalResults, from, to, results } = response.searchMedia.jsondata;

  const uniqIds = uniq(results.map((media) => media.recording.recordingId));
  const descriptionMapper = await getDescriptions(uniqIds, gql);

  const files: VFile[] = results.map((media) => {
    const {
      recordingId,
      createdTime,
      modifiedTime,
      parentTreeObjectIds,
      programLiveImage,
    } = media.recording;

    const { filename, filetype, mimetype, createdbyname } =
      media.context[0]['veritone-file'];

    return {
      id: recordingId,
      fileName: filename,
      fileType: filetype ?? mimetype,
      createdByName: createdbyname,
      parentTreeObjectIds,
      createdTime,
      updatedTime: modifiedTime,
      programLiveImage: programLiveImage,
      description: descriptionMapper[recordingId] || '',
    };
  });

  return {
    results: files,
    totalResults: totalResults.value,
    limit,
    from,
    to,
  };
}
