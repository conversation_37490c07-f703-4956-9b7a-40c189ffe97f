import {
  When,
  Then,
  Before,
  Given,
  DataTable,
} from '@badeball/cypress-cucumber-preprocessor';
import { SettingsGraphql } from '../../../support/helperFunction/settingsHelper';
import { settingPage } from '../../../pages/settingPage';
import '../common-setting/common-setting';

Before(() => {
  cy.interceptGraphQLQuery(
    SettingsGraphql.FetchSettingStatuses,
    'fetchStatusCode'
  );
  cy.interceptGraphQLQuery(SettingsGraphql.DeleteQuery, 'deleteStatus');
});

Given('The user logins successfully', () => {
  cy.LoginLandingPage();
});

Given('The user logins as {string}', (userName: string) => {
  cy.loginAsUser(userName);
});

Given('The user deletes status if exists', (statusName: DataTable) => {
  cy.deleteStatusByName(statusName);
});

Given(
  'The user creates a default status name {string}',
  (StatusName: string) => {
    settingPage.createDefaultStatus(StatusName);
  }
);

When('The user selects color {string}', (color: string) => {
  settingPage.selectColor(color);
});

When('The user clicks the color icon next to any status in the list', () => {
  settingPage.selectColorIcon();
});

When('The user selects a new color from the color scale', () => {
  settingPage.selectColorFromTheColorScale();
});

When(
  'The user inputs the HEX value manually {string}',
  (ColorInput: string) => {
    settingPage.inputHEXColor(ColorInput);
  }
);

When('The user clicks away from the dialog to close', () => {
  settingPage.clickAwayToCloseDialog();
});

Then('An error appears showing that the name is not valid', () => {
  settingPage.errorMessageIsShown();
});

Then(
  'The affected statuses should have updated colors {string}',
  (UpdatedColor: string) => {
    settingPage.checkUpdatedColor(UpdatedColor);
  }
);
