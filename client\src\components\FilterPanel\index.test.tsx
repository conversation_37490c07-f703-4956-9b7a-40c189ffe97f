import { configureAppStore } from '@store/index';
import { toggleOpenFilterDrawer } from '@store/modules/caseDetail/slice';
import { act, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Provider } from 'react-redux';
import { describe, expect, it, vi } from 'vitest';
import Filter from '.';
import { render } from '../../../test/render';
import {
  SettingsSliceState,
  initialState as SettingInitialState,
} from '@store/modules/settings/slice';

const initialStateMock: {
  settings: SettingsSliceState;
} = {
  settings: {
    ...SettingInitialState,
    fetchedStatuses: {
      statuses: [
        { id: '1', label: 'Pending', active: true, color: 'blue' },
        { id: '2', label: 'Completed', active: true, color: 'green' },
        { id: '3', label: 'Inactive', active: false, color: 'gray' },
      ],
      sdoId: 'statusSchemaId',
      status: 'complete',
    },
  },
};

describe('SearchFilter', () => {
  it('should show drawer content correctly', async () => {
    const store = configureAppStore();
    store.dispatch = vi.fn();

    render(
      <Provider store={store}>
        <Filter isCaseDetail />
      </Provider>
    );

    const closeIcon = screen.getByTestId('CloseIcon');
    expect(screen.getByText('Filters')).toBeInTheDocument();
    expect(closeIcon).toBeInTheDocument();

    userEvent.click(closeIcon);
    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledWith(toggleOpenFilterDrawer());
    });

    const cancelBtn = screen.getByRole('button', { name: /cancel/i });
    const applyBtn = screen.getByRole('button', { name: /apply/i });
    expect(cancelBtn).toBeInTheDocument();
    expect(applyBtn).toBeDisabled();

    userEvent.click(cancelBtn);
    await waitFor(() => {
      expect(store.dispatch).toHaveBeenCalledWith(toggleOpenFilterDrawer());
    });
  });

  it('apply button should be enabled when change', async () => {
    render(<Filter isCaseDetail />);

    const applyBtn = screen.getByRole('button', { name: /apply/i });
    expect(applyBtn).toBeDisabled();

    await waitFor(() => {
      expect(
        screen.getByPlaceholderText(/words in transcription/i)
      ).toBeInTheDocument();
    });

    userEvent.type(
      screen.getByPlaceholderText(/words in transcription/i),
      'test transcription'
    );
    await waitFor(() => {
      expect(applyBtn).toBeEnabled();
    });
  });

  it('should empty field values when the Reset All link is clicked', async () => {
    render(<Filter />);

    await waitFor(() => {
      expect(
        screen.getByPlaceholderText(/words in transcription/i)
      ).toBeInTheDocument();
    });

    const transcriptWordsField = screen.getByPlaceholderText<HTMLInputElement>(
      /words in transcription/i
    );
    const keywordSearchField = screen.getByTestId<HTMLInputElement>(
      'keyword-search-input'
    );

    // Type some text into the transcript words field
    userEvent.type(transcriptWordsField, 'test transcription');
    await waitFor(() => {
      expect(transcriptWordsField.value).toBe('test transcription');
    });

    const searchText = 'keyword search';

    // Type some text into the keyword search field
    userEvent.type(keywordSearchField, searchText);
    await waitFor(() => {
      expect(keywordSearchField.value).toBe(searchText);
    });

    // Click the reset all link
    const resetAllLink = screen.getByTestId('filter-panel-reset-button');
    userEvent.click(resetAllLink);

    // Now the fields are empty
    await waitFor(() => {
      expect(transcriptWordsField?.value).toBe('');
      expect(keywordSearchField?.value).toBe('');
    });
  });

  it('should render CaseInformation section and fetch caseStatus data correctly', () => {
    const store = configureAppStore(initialStateMock);

    render(
      <Provider store={store}>
        <Filter isCaseDetail />
      </Provider>,
      { container: document.body }
    );

    expect(screen.getByText('Case Information')).toBeInTheDocument();
    expect(screen.getByText('Case Status')).toBeInTheDocument();
    expect(screen.getByText('Case ID')).toBeInTheDocument();
    expect(screen.getByText('Upload Date')).toBeInTheDocument();

    const caseStatusDropdown = screen.getByTestId('caseStatus');
    expect(caseStatusDropdown).toBeInTheDocument();
  });

  it('should clear the Case Status select menu when the X button is clicked', () => {
    const store = configureAppStore(initialStateMock);

    render(
      <Provider store={store}>
        <Filter />
      </Provider>,
      { container: document.body }
    );

    // Open the Case Information accordion
    const caseInfoHeader = screen.getByTestId(
      'filter-accordion-case-information-header'
    );
    expect(caseInfoHeader).toBeInTheDocument();
    userEvent.click(caseInfoHeader);

    expect(screen.getByText('Case Information')).toBeInTheDocument();
    expect(screen.getByText('Case Status')).toBeInTheDocument();
    expect(screen.getByText('Case ID')).toBeInTheDocument();
    expect(screen.getByText('Upload Date')).toBeInTheDocument();

    const selectMenus = screen.getAllByRole('combobox');
    act(() => {
      userEvent.click(selectMenus[2]);
    });

    // The caseStatus dropdown should be open now, and have an option called "Completed"
    const option = screen.getByText('Completed');

    // Select the "Completed" option
    act(() => {
      userEvent.click(option);
    });

    // The Select control now has a clear button 'X'
    const clearButton = screen.getByTestId('filter-select-clear-caseStatus');
    expect(clearButton).toBeInTheDocument();

    const completedOption = screen.getByTestId('caseStatus-2');
    expect(completedOption).toHaveAttribute('aria-selected', 'true');

    // Click the clear button
    userEvent.click(clearButton);

    // The caseStatus select menu should now be empty
    expect(completedOption).toHaveAttribute('aria-selected', 'false');
  });

  it('should clear the File Status select menu when the X button is clicked', () => {
    const store = configureAppStore();

    render(
      <Provider store={store}>
        <Filter />
      </Provider>,
      { container: document.body }
    );

    const caseInfoHeader = screen.getByTestId(
      'filter-accordion-case-information-header'
    );
    expect(caseInfoHeader).toBeInTheDocument();
    userEvent.click(caseInfoHeader);

    expect(screen.getByText('Case Information')).toBeInTheDocument();
    expect(screen.getByText('Case Status')).toBeInTheDocument();
    expect(screen.getByText('Case ID')).toBeInTheDocument();
    expect(screen.getByText('Upload Date')).toBeInTheDocument();
    expect(screen.getByText('File Status')).toBeInTheDocument();

    const selectMenus = screen.getAllByRole('combobox');
    act(() => {
      userEvent.click(selectMenus[4]);
    });

    // The fileStatus dropdown should be open now, and have an option called "Pending"
    const option = screen.getByText('Pending');

    // Select the "Pending" option
    act(() => {
      userEvent.click(option);
    });

    // The Select control now has a clear button 'X'
    const clearButton = screen.getByTestId('filter-select-clear-fileStatus');
    expect(clearButton).toBeInTheDocument();

    // The fileStatus select menu should now have the "Pending" option selected
    const pendingOption = screen.getByTestId('fileStatus-1');
    expect(pendingOption).toHaveAttribute('aria-selected', 'true');

    // Click the clear button
    userEvent.click(clearButton);

    // The caseStatus select menu should now be empty
    expect(pendingOption).toHaveAttribute('aria-selected', 'false');
  });
});
