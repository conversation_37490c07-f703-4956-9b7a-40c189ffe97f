import eslint from "@eslint/js";
import tseslint from "typescript-eslint";
import eslintConfigPrettier from "eslint-config-prettier";

// export default tseslint.config(
//   eslint.configs.recommended,
//   tseslint.configs.strict,
//   tseslint.configs.stylistic,
//   {
//     ignores: ["dist"],
//   },
//   eslintConfigPrettier,
// );

export default [
  // eslint.configs.recommended,
  // ...tseslint.configs.strict,
  // ...tseslint.configs.stylistic,
  // {
  //   ignores: ["dist"],
  // },
  eslintConfigPrettier
];