import { FilterFormValue } from '@shared-types/types';
import { configureAppStore } from '@store/index';
import { screen } from '@testing-library/react';
import { ControllerRenderProps } from 'react-hook-form';
import { Provider } from 'react-redux';
import { describe, expect, it, vi } from 'vitest';
import CognitionAutocomplete, { Mode } from '.';
import { render } from '../../../test/render';

describe('CognitionAutocomplete', () => {
  it('should only render terms in both autocomplete and preview', () => {
    const initialState = {
      search: {
        entitySearch: { status: 'idle', data: [] },
        librarySearch: { status: 'idle', data: [] },
        objectSearch: { status: 'idle', data: [] },
      },
    };

    const store = configureAppStore(initialState);

    const mockField: ControllerRenderProps<FilterFormValue, 'faceDetections'> =
      {
        value: [
          {
            id: 'string-id-1',
            value: 'test-1',
            type: 'value',
            cognitionType: 'string',
            isValid: false,
            alias: 'A',
          },
        ],
        onChange: vi.fn(),
        onBlur: vi.fn(),
        name: 'faceDetections',
        ref: vi.fn(),
      };

    render(
      <Provider store={store}>
        <CognitionAutocomplete field={mockField} mode={Mode.FACE} />
      </Provider>
    );

    expect(screen.getByText('test-1')).toBeInTheDocument();
    expect(
      screen.getByTestId(`cognition-preview-item-string-id-1`).textContent
    ).toBe('A');
  });
});
