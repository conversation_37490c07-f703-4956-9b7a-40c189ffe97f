import { configureAppStore } from '@store/index';
import { SearchMediaResponse } from '@store/modules/search/searchFiles';
import {
  ResultCategory,
  SearchResults,
  SearchSliceState,
  initialState as searchInitialState,
} from '@store/modules/search/slice';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { MemoryRouter } from 'react-router';
import { describe, expect, it } from 'vitest';
import GroupedView from '.';
import { render } from '../../../../../test/render';

const initialState: {
  search: SearchSliceState;
} = {
  search: {
    ...searchInitialState,
    categories: [
      {
        id: 'filename',
        category: 'filename',
        color: 'var(--background-filename)',
        expanded: true,
      },
      {
        id: 'transcription',
        category: 'transcription',
        color: 'var(--background-transcription)',
        expanded: true,
        checked: true,
      },
      {
        id: 'text-recognition',
        category: 'textRecognition',
        color: 'var(--background-text-recognition)',
        expanded: true,
        checked: true,
      },
    ],
  },
};

const results: SearchMediaResponse = {
  searchMedia: {
    jsondata: {
      results: [
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '7777777777',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/7777777777',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            parentTreeObjectIds: [],
            creator: '',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '7777777777',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '888888888',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/888888888',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            parentTreeObjectIds: [],
            creator: '',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '888888888',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '9999999999',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/9999999999',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            parentTreeObjectIds: [],
            creator: '',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '444444444',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '100000000',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/100000000',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            parentTreeObjectIds: [],
            creator: '',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '100000000',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '1200000000',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/1200000000',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            parentTreeObjectIds: [],
            creator: '',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '1200000000',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
        {
          recording: {
            absoluteStartTimeMs: '2025-04-05T04:01:06.000Z',
            absoluteStopTimeMs: '2025-04-05T04:02:06.000Z',
            recordingId: '130000000',
            fileLocation:
              'https://stage.us-1.veritone.com/media-streamer/download/tdo/130000000',
            fileType: 'audio/mp4',
            programId: '188236',
            programName: 'scheduled_job_test_1743825513',
            mediaSourceId: '70927',
            mediaSourceTypeId: '1',
            relativeStartTimeMs: 60000,
            relativeStopTimeMs: 120000,
            createdTime: '2025-04-05T03:59:09.000Z',
            modifiedTime: '2025-04-05T04:10:49.000Z',
            sliceTime: 1743825666,
            mediaStartTime: 1743825606,
            aibDuration: 120,
            isOwn: false,
            hitStartTime: 1743825674,
            hitEndTime: 1743825674,
            programLiveImage: '',
            tags: [],
            parentTreeObjectIds: [],
            creator: '',
          },
          startDateTime: 1743825666,
          stopDateTime: 1743825726,
          context: [
            {
              'veritone-file': {
                filename: '130000000',
                mimetype: 'audio/mp4',
                segmented: true,
                audionumchannels: 2,
                audiosamplerate: 44100,
                duration: 120,
                hasaudio: true,
                hasvideo: false,
                height: 0,
                width: 0,
                videoframerate: 0,
              },
            },
          ],
          hits: [
            {
              transcript: {
                transcript: [
                  {
                    hits: [
                      {
                        queryTerm: 'test',
                        startTime: 68.2,
                        endTime: 68.55,
                      },
                    ],
                    startTime: 60.01,
                    endTime: 80.47,
                    text: "the originator. And boy, do I need a perpetrator. But I'm much greater. The best guess, I guess the just the best. Just fiscal fiscal test for contest. Oh, that's just addressed with best one. That's the Bless the powder Trap manifest Democrat from Minnesota. Some say sit on the stuck not stopping the group until we finish the climax climax",
                  },
                ],
                source: '',
                version: '',
              },
            },
          ],
        },
      ],
      totalResults: {
        value: 10000,
        relation: '',
      },
      limit: 50,
      from: 50,
      to: 100,
      searchToken: '',
      timestamp: 0,
    },
  },
};

const mockSearchResults: {
  [group in ResultCategory]?: SearchResults;
} = {
  transcription: {
    data: results,
    pagination: {
      offset: 10,
      limit: 20,
    },
    status: 'success',
  },
  textRecognition: {
    data: results,
    pagination: {
      offset: 20,
      limit: 30,
    },
    status: 'success',
  },
};

describe('GroupedView', () => {
  it('should render', () => {
    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <GroupedView
            groupedResults={mockSearchResults}
            onPageChange={() => {}}
            onPageSizeChange={() => {}}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.getByTestId('grouped-view')).toBeInTheDocument();
  });

  it('should render 2 accordions with data', () => {
    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <GroupedView
            groupedResults={mockSearchResults}
            onPageChange={() => {}}
            onPageSizeChange={() => {}}
          />
        </MemoryRouter>
      </Provider>
    );

    expect(screen.queryAllByTestId('accordion-header')).toHaveLength(2);
    expect(
      screen.getByTestId('accordion-name-transcription')
    ).toBeInTheDocument();
    expect(
      screen.getByTestId('accordion-name-textRecognition')
    ).toBeInTheDocument();

    expect(
      screen.getByTestId('accordion-header-result-count-transcription')
    ).toHaveTextContent('>10000 Results');
    expect(
      screen.getByTestId('accordion-header-result-count-textRecognition')
    ).toHaveTextContent('>10000 Results');
  });

  it('should check all correctly', () => {
    const store = configureAppStore(initialState);

    render(
      <Provider store={store}>
        <MemoryRouter>
          <GroupedView
            groupedResults={mockSearchResults}
            onPageChange={() => {}}
            onPageSizeChange={() => {}}
          />
        </MemoryRouter>
      </Provider>
    );

    const checkAllBox = screen.getByTestId('grouped-list-header__checkbox');
    expect(checkAllBox).not.toBeChecked();
    fireEvent.click(checkAllBox);

    waitFor(() => {
      expect(checkAllBox).toBeChecked();
    });

    waitFor(() => {
      expect(
        screen.getByTestId('accordion__check-box__check-all-transcription')
      ).toBeChecked();
      expect(
        screen.getByTestId('accordion__check-box__check-all-textRecognition')
      ).toBeChecked();
    });
  });
});
