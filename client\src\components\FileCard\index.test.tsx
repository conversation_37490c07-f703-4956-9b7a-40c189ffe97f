import { screen, waitFor } from '@testing-library/dom';
import userEvent from '@testing-library/user-event';
import { describe, expect, it, vi } from 'vitest';
import FileCard from '.';
import { render } from '../../../test/render';

const defaultProps = {
  fileId: 'fileId',
  fileName: 'test.mp4',
  fileDuration: 50000,
  caseId: '123',
  dateUploaded: '2021-10-10',
  thumbnailUrl:
    'https://www.google.com/imgres?q=image&imgurl=https%3A%2F%2Fletsenhance.io%2Fstatic%2F73136da51c245e80edc6ccfe44888a99%2F1015f%2FMainBefore.jpg&imgrefurl=https%3A%2F%2Fletsenhance.io%2F&docid=-t22bY2ix3gHaM&tbnid=D2e1clQQJsbJwM&vet=12ahUKEwiyg_PN6vOLAxVCJUQIHcQ0OpEQM3oECHcQAA..i&w=1280&h=720&hcb=2&ved=2ahUKEwiyg_PN6vOLAxVCJUQIHcQ0OpEQM3oECHcQAA',
  fileType: 'video',
  blurred: false,
  isChecked: false,
  onCheck: vi.fn(),
  onMove: vi.fn(),
  onOpenEditMetadataDrawer: vi.fn(),
  onViewFile: vi.fn(),
  onViewCase: vi.fn(),
  onDelete: vi.fn(),
};

describe('File Card', () => {
  it('renders file card w/o blur correctly', () => {
    const { getByTestId } = render(<FileCard {...defaultProps} />);

    expect(getByTestId('file-card-image')).not.toBeNull();
    expect(getByTestId('file-card-image-img')).not.toBeNull();
    expect(getByTestId('file-card-image-img')).not.toHaveClass('blurred');
  });

  it('renders file card w/ blur correctly', () => {
    const { getByTestId } = render(<FileCard {...defaultProps} blurred />);

    expect(getByTestId('file-card-image')).not.toBeNull();
    expect(getByTestId('file-card-image-img')).not.toBeNull();
    expect(getByTestId('file-card-image-img')).toHaveClass('blurred');
  });

  it('should render menu action correctly', async () => {
    render(<FileCard {...defaultProps} blurred />);

    const menuIcon = screen.getByTestId('file-card-menu-icon');

    userEvent.click(menuIcon);
    await waitFor(() => {
      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(screen.getByText('Move')).toBeInTheDocument();
      expect(screen.getByText('Edit Metadata')).toBeInTheDocument();
      expect(screen.getByText('View File')).toBeInTheDocument();
      expect(screen.getByText('View Case')).toBeInTheDocument();
      expect(screen.getByText('Delete')).toBeInTheDocument();
    });

    userEvent.click(screen.getByText('Edit Metadata'));
    expect(defaultProps.onOpenEditMetadataDrawer).toBeCalledWith(
      defaultProps.fileId
    );
  });
});
