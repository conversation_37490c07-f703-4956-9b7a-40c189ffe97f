import { useEffect } from 'react';
import { useNavigate } from 'react-router';

interface Props {
  tdoId?: string;
  navigationPath: string;
}

export const useDataDetailsPanel = ({ tdoId, navigationPath }: Props) => {
  const navigate = useNavigate();
  useEffect(() => {
    if (window.aiware && tdoId) {
      const panelId = 'DATA_CENTER_DATA_DETAILS_PANEL';

      const mountPanel = () => {
        if (window.aiware) {
          window.aiware.mountPanel({
            panelId,
            microFrontend: {
              name: panelId,
              config: {
                tdoId,
                viewerUrl: 'https://viewers.stage.us-1.veritone.com/asset', // TODO: Replace and remove when fixed
                // actions: [
                //   {
                //     tooltip: "I'm a tooltip",
                //     // icon: <IconViewerCenter />,
                //     onClick: () => {
                //       alert('Custom Action - 1');
                //     },
                //   },
                //   {
                //     // icon: <IconAdminTab />,
                //     onClick: () => {
                //       alert('Custom Action - 2');
                //     },
                //   },
                // ],
                // viewers: {
                //   media: {
                //     type: 'video',
                //     url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                //   },
                //   info: [
                //     {
                //       name: 'moreDetails',
                //       label: 'More Media Details',
                //       data: [
                //         {
                //           name: 'Resource.Class',
                //           value: 'Footage',
                //         },
                //         {
                //           name: 'name',
                //         },
                //         {
                //           name: 'TE.DigitalFormat',
                //           value: 'Standard Definition',
                //         },
                //         {
                //           name: 'Rights.Reproduction',
                //         },
                //         {
                //           name: 'Title',
                //           value: "What's My Line?",
                //         },
                //         {
                //           name: 'Description',
                //           value: "What's My Line?",
                //         },
                //         {
                //           value: '1972',
                //           name: 'Description.Date',
                //         },
                //       ],
                //     },
                //     {
                //       name: 'details',
                //       data: [
                //         {
                //           name: 'name',
                //         },
                //       ],
                //       sections: [
                //         {
                //           children: [
                //             {
                //               name: 'Title',
                //               value: 'Celebrity Name Game',
                //             },
                //             {
                //               name: 'Description',
                //               value:
                //                 'Two teams, each paired with a celebrity, improvise clues to identify as many correct answers as possible for a chance to win $20,000',
                //             },
                //             {
                //               name: 'Description.Date',
                //             },
                //           ],
                //         },
                //         {
                //           title: 'Specs',
                //           children: [
                //             {
                //               name: 'Resource.Class',
                //               value: 'Footage',
                //             },
                //             {
                //               name: 'TE.DigitalFormat',
                //               value: 'High Definition',
                //             },
                //             {
                //               name: 'Rights.Reproduction',
                //             },
                //           ],
                //         },
                //       ],
                //     },
                //   ],
                // },
              },
            },
            panelConfig: {
              size: 'xxlarge',
              type: 'APP_BAR_PANEL_TEMPLATE',
            },
          });
        } else {
          navigate(navigationPath);
        }
      };

      mountPanel();

      let isPanelMounted = false;

      const observer = new MutationObserver(() => {
        const panel = document.getElementById(panelId);

        // Detect when panel is mounted
        if (panel && !isPanelMounted) {
          isPanelMounted = true;
        }

        // Detect when panel is unmounted
        if (!panel && isPanelMounted) {
          isPanelMounted = false;
          window.aiware?.unmountPanel(panelId);
          navigate(navigationPath);
        }
      });

      const onPopState = () => {
        const panel = document.getElementById(panelId);
        if (panel) {
          window.aiware?.unmountPanel(panelId);
          navigate(navigationPath);
        } else if (location.pathname.includes(`/data-details/${tdoId}`)) {
          mountPanel();
        }
      };

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      window.addEventListener('popstate', onPopState);

      return () => {
        observer.disconnect();
        window.removeEventListener('popstate', onPopState);
        const panel = document.getElementById(panelId);
        if (panel) {
          window.aiware?.unmountPanel(panelId);
        }
      };
    }
  }, [window.aiware, tdoId, navigate, navigationPath, location.pathname]);
};
