import { configureAppStore } from '@store/index';
import {
  CaseManagerSliceState,
  initialState as CaseManagerState,
} from '@store/modules/caseManager/slice';
import { waitFor } from '@testing-library/dom';
import { G<PERSON>Api } from '@utils/helpers';
import {
  baseGraphQLApi,
  baseGraphQLApiThrowError,
} from '@utils/helpers/gqlApi/baseGraphQLApi';
import { afterEach } from 'node:test';
import { describe, expect, it, vi } from 'vitest';
import {
  mockGetFolderResponse,
  mockSearchMediaResponse,
  newCaseDetailState,
  searchByNameAndSortByDate,
} from './fixtures';
import {
  CaseDetailSliceState,
  initialState as caseDetailInitialState,
  getFiles,
  setLimit,
  setSortBy,
  setSortDirection,
} from './slice';

vi.mock('../../../utils/helpers/gqlApi/baseGraphQLApi', () => ({
  baseGraphQLApiThrowError: vi.fn(),
  baseGraphQLApi: vi.fn(),
}));

const initialStateForMock: {
  caseManager: CaseManagerSliceState;
  caseDetail: CaseDetailSliceState;
  config: Window['config'];
  auth: { sessionToken: string };
} = {
  caseManager: {
    ...CaseManagerState,
    rootFolder: {
      status: 'idle',
      error: '',
      id: 'rootFolderId123',
    },
  },
  caseDetail: {
    ...caseDetailInitialState,
  },
  config: {
    apiRoot: 'apiroot123',
    graphQLEndpoint: 'graphQLEndpoint123',
    veritoneAppId: 'veritoneAppId123',
    registryIds: {
      caseRegistryId: 'caseRegistryId123',
      statusRegistryId: 'statusRegistryId',
      tagRegistryId: '',
      evidenceTypeRegistryId: '',
    },
    nodeEnv: 'dev',
  },
  auth: { sessionToken: 'sessionToken123' },
};

describe('caseDetailSlice', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should get files by name and sort by create time', async () => {
    const searchMediaSpy = vi.spyOn(GQLApi.prototype, 'searchMedia');

    vi.spyOn(GQLApi.prototype, 'getFolder').mockImplementationOnce(() =>
      Promise.resolve(mockGetFolderResponse)
    );
    vi.spyOn(GQLApi.prototype, 'getTdoDetails').mockImplementationOnce(() =>
      Promise.resolve({ tags: [], veritoneFile: {} })
    );

    vi.mocked(baseGraphQLApiThrowError).mockImplementationOnce(() =>
      Promise.resolve(mockSearchMediaResponse)
    );

    vi.mocked(baseGraphQLApi)
      .mockImplementationOnce(() =>
        Promise.resolve({
          data: {
            temporalDataObjects: {
              count: 1,
              limit: 10,
              offset: 0,
              records: [{ id: 'tdo1', description: 'description1' }],
            },
          },
        })
      )
      .mockImplementationOnce(() =>
        Promise.resolve({
          data: {
            temporalDataObjects: {
              count: 1,
              limit: 10,
              offset: 0,
              records: [{ id: 'tdo2', description: 'description2' }],
            },
          },
        })
      );

    const mockedStore = configureAppStore(initialStateForMock);

    mockedStore.dispatch(setSortBy('createdTime'));
    mockedStore.dispatch(setSortDirection('desc'));
    mockedStore.dispatch(setLimit(10));

    // dispatch the getFiles thunk
    await mockedStore.dispatch(
      getFiles({
        folderId: 'folderId123',
        fileName: 'Test',
      })
    );

    // get the updated state
    const newState = mockedStore.getState();

    await waitFor(() => {
      expect(searchMediaSpy).toHaveBeenCalledOnce();
    });
    searchMediaSpy.mockClear();

    expect(baseGraphQLApiThrowError).toHaveBeenCalledWith(
      searchByNameAndSortByDate
    );

    expect(newState.caseDetail.files).toStrictEqual(newCaseDetailState);
  });
});
